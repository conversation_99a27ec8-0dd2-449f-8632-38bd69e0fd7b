import '../models/invoice_models.dart';
import '../models/quotation_models.dart';
import '../services/firebase_invoice_service.dart';

class InvoiceRepository {
  final FirebaseInvoiceService _firebaseService;

  InvoiceRepository({FirebaseInvoiceService? firebaseService})
      : _firebaseService = firebaseService ?? FirebaseInvoiceService.instance;

  // =================== INVOICE OPERATIONS ===================

  /// Create a new invoice
  Future<String> createInvoice(Invoice invoice) async {
    return await _firebaseService.createInvoice(invoice);
  }

  /// Update an existing invoice
  Future<void> updateInvoice(String invoiceId, Invoice invoice) async {
    await _firebaseService.updateInvoice(invoiceId, invoice);
  }

  /// Delete an invoice
  Future<void> deleteInvoice(String invoiceId) async {
    await _firebaseService.deleteInvoice(invoiceId);
  }

  /// Get a single invoice by ID
  Future<Invoice?> getInvoiceById(String invoiceId) async {
    return await _firebaseService.getInvoiceById(invoiceId);
  }

  /// Get all invoices with optional filtering
  Future<List<Invoice>> getInvoices({
    InvoiceStatus? status,
    PaymentStatus? paymentStatus,
    String? clientId,
    DateTime? startDate,
    DateTime? endDate,
    String? searchTerm,
    int? limit,
    String? orderBy = 'createdAt',
    bool descending = true,
  }) async {
    return await _firebaseService.getInvoices(
      status: status,
      paymentStatus: paymentStatus,
      clientId: clientId,
      startDate: startDate,
      endDate: endDate,
      searchTerm: searchTerm,
      limit: limit,
      orderBy: orderBy,
      descending: descending,
    );
  }

  /// Create invoice from quotation
  Future<Invoice> createInvoiceFromQuotation(
    Quotation quotation, {
    DateTime? issueDate,
    DateTime? dueDate,
    InvoiceTemplate template = InvoiceTemplate.modern,
  }) async {
    return await _firebaseService.createInvoiceFromQuotation(
      quotation,
      issueDate: issueDate,
      dueDate: dueDate,
      template: template,
    );
  }

  /// Generate a new invoice number
  Future<String> generateInvoiceNumber() async {
    return await _firebaseService.generateInvoiceNumber();
  }

  // =================== STATUS OPERATIONS ===================

  /// Update invoice status
  Future<void> updateInvoiceStatus(String invoiceId, InvoiceStatus status) async {
    await _firebaseService.updateInvoiceStatus(invoiceId, status);
  }

  /// Update payment status
  Future<void> updatePaymentStatus(String invoiceId, PaymentStatus paymentStatus) async {
    await _firebaseService.updatePaymentStatus(invoiceId, paymentStatus);
  }

  /// Mark invoice as sent
  Future<void> markAsSent(String invoiceId) async {
    await updateInvoiceStatus(invoiceId, InvoiceStatus.sent);
  }

  /// Mark invoice as paid
  Future<void> markAsPaid(String invoiceId, {PaymentRecord? paymentRecord}) async {
    if (paymentRecord != null) {
      await addPayment(invoiceId, paymentRecord);
    } else {
      await updateInvoiceStatus(invoiceId, InvoiceStatus.paid);
      await updatePaymentStatus(invoiceId, PaymentStatus.paid);
    }
  }

  /// Cancel invoice
  Future<void> cancelInvoice(String invoiceId) async {
    await updateInvoiceStatus(invoiceId, InvoiceStatus.cancelled);
    await updatePaymentStatus(invoiceId, PaymentStatus.cancelled);
  }

  // =================== PAYMENT OPERATIONS ===================

  /// Add a payment record to an invoice
  Future<void> addPayment(String invoiceId, PaymentRecord payment) async {
    await _firebaseService.addPayment(invoiceId, payment);
  }

  // =================== ANALYTICS & REPORTING ===================

  /// Get invoice statistics
  Future<Map<String, dynamic>> getInvoiceStatistics() async {
    return await _firebaseService.getInvoiceStatistics();
  }

  /// Get overdue invoices
  Future<List<Invoice>> getOverdueInvoices() async {
    return await _firebaseService.getOverdueInvoices();
  }

  /// Get invoices by status
  Future<List<Invoice>> getInvoicesByStatus(InvoiceStatus status) async {
    return await getInvoices(status: status);
  }

  /// Get invoices by payment status
  Future<List<Invoice>> getInvoicesByPaymentStatus(PaymentStatus paymentStatus) async {
    return await getInvoices(paymentStatus: paymentStatus);
  }

  /// Get invoices for a specific client
  Future<List<Invoice>> getInvoicesForClient(String clientId) async {
    return await getInvoices(clientId: clientId);
  }

  /// Get invoices within date range
  Future<List<Invoice>> getInvoicesInDateRange(DateTime startDate, DateTime endDate) async {
    return await getInvoices(startDate: startDate, endDate: endDate);
  }

  /// Search invoices
  Future<List<Invoice>> searchInvoices(String searchTerm) async {
    return await getInvoices(searchTerm: searchTerm);
  }

  /// Check if invoice number exists
  Future<bool> invoiceNumberExists(String invoiceNumber) async {
    final invoices = await getInvoices();
    return invoices.any((invoice) => invoice.invoiceNumber == invoiceNumber);
  }

  /// Get next available invoice number
  Future<String> getNextInvoiceNumber() async {
    return await generateInvoiceNumber();
  }

  /// Get invoice count by status
  Future<Map<InvoiceStatus, int>> getInvoiceCountByStatus() async {
    final invoices = await getInvoices();
    final Map<InvoiceStatus, int> counts = {};
    
    for (final status in InvoiceStatus.values) {
      counts[status] = invoices.where((invoice) => invoice.status == status).length;
    }
    
    return counts;
  }

  /// Get payment status distribution
  Future<Map<PaymentStatus, int>> getPaymentStatusDistribution() async {
    final invoices = await getInvoices();
    final Map<PaymentStatus, int> distribution = {};
    
    for (final status in PaymentStatus.values) {
      distribution[status] = invoices.where((invoice) => invoice.paymentStatus == status).length;
    }
    
    return distribution;
  }

  /// Get monthly invoice summary
  Future<Map<String, dynamic>> getMonthlyInvoiceSummary(int year, int month) async {
    final startDate = DateTime(year, month, 1);
    final endDate = DateTime(year, month + 1, 0);
    
    final invoices = await getInvoicesInDateRange(startDate, endDate);
    
    final totalInvoices = invoices.length;
    final totalAmount = invoices.fold(0.0, (sum, invoice) => sum + invoice.totalAmount);
    final paidAmount = invoices.fold(0.0, (sum, invoice) => sum + invoice.paidAmount);
    final pendingAmount = totalAmount - paidAmount;
    
    return {
      'totalInvoices': totalInvoices,
      'totalAmount': totalAmount,
      'paidAmount': paidAmount,
      'pendingAmount': pendingAmount,
      'invoices': invoices,
    };
  }

  /// Get template usage statistics
  Future<Map<InvoiceTemplate, int>> getTemplateUsageStats() async {
    final invoices = await getInvoices();
    final Map<InvoiceTemplate, int> stats = {};
    
    for (final template in InvoiceTemplate.values) {
      stats[template] = invoices.where((invoice) => invoice.template == template).length;
    }
    
    return stats;
  }
}
