import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../models/invoice_models.dart';

class InvoiceFilterWidget extends StatefulWidget {
  final ValueChanged<InvoiceStatus?>? onStatusFilterChanged;
  final ValueChanged<PaymentStatus?>? onPaymentStatusFilterChanged;
  final Function(DateTime?, DateTime?)? onDateRangeChanged;
  final VoidCallback? onClearFilters;

  const InvoiceFilterWidget({
    super.key,
    this.onStatusFilterChanged,
    this.onPaymentStatusFilterChanged,
    this.onDateRangeChanged,
    this.onClearFilters,
  });

  @override
  State<InvoiceFilterWidget> createState() => _InvoiceFilterWidgetState();
}

class _InvoiceFilterWidgetState extends State<InvoiceFilterWidget> {
  InvoiceStatus? _selectedStatus;
  PaymentStatus? _selectedPaymentStatus;
  DateTimeRange? _selectedDateRange;
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final hasActiveFilters = _selectedStatus != null ||
        _selectedPaymentStatus != null ||
        _selectedDateRange != null;

    return Card(
      margin: EdgeInsets.zero,
      child: Column(
        children: [
          // Filter Header
          InkWell(
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(
                    Icons.filter_list,
                    color: hasActiveFilters ? theme.primaryColor : null,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Filters',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: hasActiveFilters ? theme.primaryColor : null,
                      fontWeight: hasActiveFilters ? FontWeight.bold : null,
                    ),
                  ),
                  if (hasActiveFilters) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: theme.primaryColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getActiveFilterCount().toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                  const Spacer(),
                  if (hasActiveFilters)
                    TextButton(
                      onPressed: _clearAllFilters,
                      child: const Text('Clear All'),
                    ),
                  Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                  ),
                ],
              ),
            ),
          ),
          
          // Filter Content
          if (_isExpanded) ...[
            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status Filter
                  _buildFilterSection(
                    'Invoice Status',
                    _buildStatusFilter(),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Payment Status Filter
                  _buildFilterSection(
                    'Payment Status',
                    _buildPaymentStatusFilter(),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Date Range Filter
                  _buildFilterSection(
                    'Date Range',
                    _buildDateRangeFilter(),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFilterSection(String title, Widget content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        content,
      ],
    );
  }

  Widget _buildStatusFilter() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        _buildFilterChip(
          'All',
          _selectedStatus == null,
          () => _updateStatusFilter(null),
        ),
        ...InvoiceStatus.values.map((status) {
          return _buildFilterChip(
            status.toString().split('.').last.toUpperCase(),
            _selectedStatus == status,
            () => _updateStatusFilter(status),
          );
        }),
      ],
    );
  }

  Widget _buildPaymentStatusFilter() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        _buildFilterChip(
          'All',
          _selectedPaymentStatus == null,
          () => _updatePaymentStatusFilter(null),
        ),
        ...PaymentStatus.values.map((status) {
          return _buildFilterChip(
            status.toString().split('.').last.toUpperCase(),
            _selectedPaymentStatus == status,
            () => _updatePaymentStatusFilter(status),
          );
        }),
      ],
    );
  }

  Widget _buildDateRangeFilter() {
    final dateFormat = DateFormat('MMM dd, yyyy');
    
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _selectDateRange,
                icon: const Icon(Icons.date_range),
                label: Text(
                  _selectedDateRange != null
                      ? '${dateFormat.format(_selectedDateRange!.start)} - ${dateFormat.format(_selectedDateRange!.end)}'
                      : 'Select Date Range',
                ),
              ),
            ),
            if (_selectedDateRange != null) ...[
              const SizedBox(width: 8),
              IconButton(
                onPressed: () => _updateDateRange(null),
                icon: const Icon(Icons.clear),
                tooltip: 'Clear Date Range',
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: [
            _buildQuickDateFilter('Today', _getTodayRange()),
            _buildQuickDateFilter('This Week', _getThisWeekRange()),
            _buildQuickDateFilter('This Month', _getThisMonthRange()),
            _buildQuickDateFilter('Last 30 Days', _getLast30DaysRange()),
          ],
        ),
      ],
    );
  }

  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    final theme = Theme.of(context);
    
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (_) => onTap(),
      selectedColor: theme.primaryColor.withOpacity(0.2),
      checkmarkColor: theme.primaryColor,
      labelStyle: TextStyle(
        color: isSelected ? theme.primaryColor : null,
        fontWeight: isSelected ? FontWeight.bold : null,
      ),
    );
  }

  Widget _buildQuickDateFilter(String label, DateTimeRange range) {
    return ActionChip(
      label: Text(label),
      onPressed: () => _updateDateRange(range),
    );
  }

  void _updateStatusFilter(InvoiceStatus? status) {
    setState(() {
      _selectedStatus = status;
    });
    widget.onStatusFilterChanged?.call(status);
  }

  void _updatePaymentStatusFilter(PaymentStatus? status) {
    setState(() {
      _selectedPaymentStatus = status;
    });
    widget.onPaymentStatusFilterChanged?.call(status);
  }

  void _updateDateRange(DateTimeRange? range) {
    setState(() {
      _selectedDateRange = range;
    });
    widget.onDateRangeChanged?.call(range?.start, range?.end);
  }

  Future<void> _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _selectedDateRange,
    );
    
    if (picked != null) {
      _updateDateRange(picked);
    }
  }

  void _clearAllFilters() {
    setState(() {
      _selectedStatus = null;
      _selectedPaymentStatus = null;
      _selectedDateRange = null;
    });
    
    widget.onStatusFilterChanged?.call(null);
    widget.onPaymentStatusFilterChanged?.call(null);
    widget.onDateRangeChanged?.call(null, null);
    widget.onClearFilters?.call();
  }

  int _getActiveFilterCount() {
    int count = 0;
    if (_selectedStatus != null) count++;
    if (_selectedPaymentStatus != null) count++;
    if (_selectedDateRange != null) count++;
    return count;
  }

  DateTimeRange _getTodayRange() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    return DateTimeRange(start: today, end: today);
  }

  DateTimeRange _getThisWeekRange() {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    return DateTimeRange(start: startOfWeek, end: endOfWeek);
  }

  DateTimeRange _getThisMonthRange() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);
    return DateTimeRange(start: startOfMonth, end: endOfMonth);
  }

  DateTimeRange _getLast30DaysRange() {
    final now = DateTime.now();
    final thirtyDaysAgo = now.subtract(const Duration(days: 30));
    return DateTimeRange(start: thirtyDaysAgo, end: now);
  }
}
