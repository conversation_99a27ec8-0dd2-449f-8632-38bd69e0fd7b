import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';

import '../../blocs/invoice/invoice_bloc.dart';
import '../../blocs/invoice/invoice_event.dart';
import '../../blocs/invoice/invoice_state.dart';
import '../../models/invoice_models.dart';
import '../../services/pdf_service.dart';
import '../widgets/loading_widget.dart';
import 'create_invoice_screen.dart';

class InvoiceDetailScreen extends StatefulWidget {
  final Invoice invoice;

  const InvoiceDetailScreen({
    super.key,
    required this.invoice,
  });

  @override
  State<InvoiceDetailScreen> createState() => _InvoiceDetailScreenState();
}

class _InvoiceDetailScreenState extends State<InvoiceDetailScreen> {
  late Invoice _invoice;
  bool _isGeneratingPdf = false;

  @override
  void initState() {
    super.initState();
    _invoice = widget.invoice;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currencyFormat = NumberFormat.currency(symbol: '₹');
    final dateFormat = DateFormat('MMM dd, yyyy');

    return Scaffold(
      appBar: AppBar(
        title: Text(_invoice.invoiceNumber),
        actions: [
          IconButton(
            onPressed: _editInvoice,
            icon: const Icon(Icons.edit),
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'duplicate',
                child: ListTile(
                  leading: Icon(Icons.copy),
                  title: Text('Duplicate'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'share',
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text('Share PDF'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: ListTile(
                  leading: Icon(Icons.delete, color: Colors.red),
                  title: Text('Delete'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: BlocListener<InvoiceBloc, InvoiceState>(
        listener: (context, state) {
          if (state is InvoiceUpdated) {
            setState(() {
              _invoice = state.invoice;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.green,
              ),
            );
          } else if (state is InvoiceDeleted) {
            Navigator.of(context).pop();
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.orange,
              ),
            );
          } else if (state is InvoiceError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: LoadingOverlay(
          isLoading: _isGeneratingPdf,
          loadingMessage: 'Generating PDF...',
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Status and Actions Card
                _buildStatusCard(),
                const SizedBox(height: 16),
                
                // Invoice Details Card
                _buildInvoiceDetailsCard(),
                const SizedBox(height: 16),
                
                // Client Information Card
                _buildClientCard(),
                const SizedBox(height: 16),
                
                // Items Card
                _buildItemsCard(),
                const SizedBox(height: 16),
                
                // Payment Information Card
                if (_invoice.payments.isNotEmpty)
                  _buildPaymentHistoryCard(),
                
                // Notes Card
                if (_invoice.notes.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  _buildNotesCard(),
                ],
                
                // Terms Card
                if (_invoice.termsAndConditions.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  _buildTermsCard(),
                ],
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: _buildBottomActionBar(),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                _buildStatusChip(
                  _invoice.status.toString().split('.').last.toUpperCase(),
                  _getStatusColor(_invoice.status),
                ),
                const SizedBox(width: 8),
                _buildStatusChip(
                  _invoice.paymentStatus.toString().split('.').last.toUpperCase(),
                  _getPaymentStatusColor(_invoice.paymentStatus),
                ),
                const Spacer(),
                if (_invoice.isOverdue)
                  Icon(
                    Icons.warning,
                    color: Colors.red[600],
                    size: 24,
                  ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Quick Actions
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                if (_invoice.status == InvoiceStatus.draft)
                  ActionChip(
                    label: const Text('Mark as Sent'),
                    onPressed: () => _updateStatus(InvoiceStatus.sent),
                    avatar: const Icon(Icons.send, size: 18),
                  ),
                if (_invoice.paymentStatus != PaymentStatus.paid)
                  ActionChip(
                    label: const Text('Mark as Paid'),
                    onPressed: _markAsPaid,
                    avatar: const Icon(Icons.payment, size: 18),
                  ),
                if (_invoice.paymentStatus == PaymentStatus.paid && _invoice.payments.isNotEmpty)
                  ActionChip(
                    label: const Text('Add Payment'),
                    onPressed: _addPayment,
                    avatar: const Icon(Icons.add, size: 18),
                  ),
                ActionChip(
                  label: const Text('Generate PDF'),
                  onPressed: _generatePdf,
                  avatar: const Icon(Icons.picture_as_pdf, size: 18),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildInvoiceDetailsCard() {
    final dateFormat = DateFormat('MMM dd, yyyy');
    final currencyFormat = NumberFormat.currency(symbol: '₹');
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Invoice Details',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            _buildDetailRow('Invoice Number', _invoice.invoiceNumber),
            _buildDetailRow('Issue Date', dateFormat.format(_invoice.issueDate)),
            _buildDetailRow('Due Date', dateFormat.format(_invoice.dueDate)),
            _buildDetailRow('Template', _getTemplateDisplayName(_invoice.template)),
            const Divider(),
            _buildDetailRow(
              'Total Amount',
              currencyFormat.format(_invoice.totalAmount),
              valueStyle: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 18,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildClientCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Client Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                CircleAvatar(
                  radius: 24,
                  child: Text(
                    _invoice.client.name[0].toUpperCase(),
                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _invoice.client.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(_invoice.client.email),
                      if (_invoice.client.phone.isNotEmpty)
                        Text(_invoice.client.phone),
                      if (_invoice.client.address.isNotEmpty)
                        Text(
                          _invoice.client.address,
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsCard() {
    final currencyFormat = NumberFormat.currency(symbol: '₹');

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Items (${_invoice.items.length})',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _invoice.items.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final item = _invoice.items[index];
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.description,
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Text('${item.quantity} ${item.unit}'),
                          const SizedBox(width: 16),
                          Text('@ ${currencyFormat.format(item.unitPrice)}'),
                          if (item.discount > 0) ...[
                            const SizedBox(width: 16),
                            Text(
                              'Discount: ${currencyFormat.format(item.discount)}',
                              style: TextStyle(color: Colors.green[600]),
                            ),
                          ],
                          const Spacer(),
                          Text(
                            currencyFormat.format(item.total),
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              },
            ),
            const Divider(thickness: 2),

            // Summary
            _buildSummarySection(),
          ],
        ),
      ),
    );
  }

  Widget _buildSummarySection() {
    final currencyFormat = NumberFormat.currency(symbol: '₹');

    return Column(
      children: [
        _buildSummaryRow('Subtotal', currencyFormat.format(_invoice.subtotal)),
        if (_invoice.taxRate > 0)
          _buildSummaryRow(
            'Tax (${_invoice.taxRate.toStringAsFixed(1)}%)',
            currencyFormat.format(_invoice.taxAmount),
          ),
        if (_invoice.shippingCost > 0)
          _buildSummaryRow('Shipping', currencyFormat.format(_invoice.shippingCost)),
        if (_invoice.additionalCharges > 0)
          _buildSummaryRow('Additional Charges', currencyFormat.format(_invoice.additionalCharges)),
        const Divider(),
        _buildSummaryRow(
          'Total',
          currencyFormat.format(_invoice.totalAmount),
          isTotal: true,
        ),
      ],
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: isTotal ? 16 : 14,
              color: isTotal ? Theme.of(context).primaryColor : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentHistoryCard() {
    final currencyFormat = NumberFormat.currency(symbol: '₹');
    final dateFormat = DateFormat('MMM dd, yyyy');

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Payment History',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  'Total Paid: ${currencyFormat.format(_invoice.paidAmount)}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.green[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _invoice.payments.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final payment = _invoice.payments[index];
                return ListTile(
                  contentPadding: EdgeInsets.zero,
                  leading: CircleAvatar(
                    backgroundColor: Colors.green.withOpacity(0.1),
                    child: Icon(
                      Icons.payment,
                      color: Colors.green[600],
                    ),
                  ),
                  title: Text(
                    currencyFormat.format(payment.amount),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(dateFormat.format(payment.date)),
                      if (payment.method.isNotEmpty)
                        Text('Method: ${payment.method}'),
                      if (payment.reference?.isNotEmpty == true)
                        Text('Ref: ${payment.reference}'),
                    ],
                  ),
                  trailing: payment.notes?.isNotEmpty == true
                      ? IconButton(
                          icon: const Icon(Icons.note),
                          onPressed: () => _showPaymentNotes(payment.notes!),
                        )
                      : null,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notes',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(_invoice.notes),
          ],
        ),
      ),
    );
  }

  Widget _buildTermsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Terms & Conditions',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(_invoice.termsAndConditions),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {TextStyle? valueStyle}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: valueStyle ?? const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActionBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _generatePdf,
              icon: const Icon(Icons.picture_as_pdf),
              label: const Text('Generate PDF'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _editInvoice,
              icon: const Icon(Icons.edit),
              label: const Text('Edit Invoice'),
            ),
          ),
        ],
      ),
    );
  }

  // =================== HELPER METHODS ===================

  Color _getStatusColor(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return Colors.grey;
      case InvoiceStatus.sent:
        return Colors.blue;
      case InvoiceStatus.viewed:
        return Colors.orange;
      case InvoiceStatus.paid:
        return Colors.green;
      case InvoiceStatus.overdue:
        return Colors.red;
      case InvoiceStatus.cancelled:
        return Colors.red;
    }
  }

  Color _getPaymentStatusColor(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.pending:
        return Colors.orange;
      case PaymentStatus.partial:
        return Colors.blue;
      case PaymentStatus.paid:
        return Colors.green;
      case PaymentStatus.overdue:
        return Colors.red;
      case PaymentStatus.refunded:
        return Colors.purple;
      case PaymentStatus.cancelled:
        return Colors.grey;
    }
  }

  String _getTemplateDisplayName(InvoiceTemplate template) {
    switch (template) {
      case InvoiceTemplate.modern:
        return 'Modern';
      case InvoiceTemplate.classic:
        return 'Classic';
      case InvoiceTemplate.minimal:
        return 'Minimal';
      case InvoiceTemplate.professional:
        return 'Professional';
    }
  }

  // =================== ACTION METHODS ===================

  void _editInvoice() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreateInvoiceScreen(invoice: _invoice),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'duplicate':
        _duplicateInvoice();
        break;
      case 'share':
        _shareInvoice();
        break;
      case 'delete':
        _deleteInvoice();
        break;
    }
  }

  void _duplicateInvoice() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreateInvoiceScreen(invoice: _invoice),
      ),
    );
  }

  void _shareInvoice() async {
    setState(() {
      _isGeneratingPdf = true;
    });

    try {
      final pdfBytes = await PdfService.generateInvoicePdf(
        invoice: _invoice,
      );

      // Save to temporary file and share
      // Implementation depends on your file handling setup

      setState(() {
        _isGeneratingPdf = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('PDF generated and ready to share'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      setState(() {
        _isGeneratingPdf = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error generating PDF: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _deleteInvoice() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Invoice'),
        content: Text('Are you sure you want to delete invoice ${_invoice.invoiceNumber}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<InvoiceBloc>().add(DeleteInvoice(_invoice.id));
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _updateStatus(InvoiceStatus status) {
    context.read<InvoiceBloc>().add(
      UpdateInvoiceStatus(
        invoiceId: _invoice.id,
        status: status,
      ),
    );
  }

  void _markAsPaid() {
    _addPayment(markAsPaid: true);
  }

  void _addPayment({bool markAsPaid = false}) {
    final amountController = TextEditingController(
      text: markAsPaid ? _invoice.remainingAmount.toString() : '',
    );
    final methodController = TextEditingController();
    final referenceController = TextEditingController();
    final notesController = TextEditingController();
    DateTime paymentDate = DateTime.now();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(markAsPaid ? 'Mark as Paid' : 'Add Payment'),
          content: SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: amountController,
                    decoration: InputDecoration(
                      labelText: 'Amount',
                      hintText: 'Remaining: ₹${_invoice.remainingAmount.toStringAsFixed(2)}',
                      border: const OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 16),

                  InkWell(
                    onTap: () async {
                      final picked = await showDatePicker(
                        context: context,
                        initialDate: paymentDate,
                        firstDate: DateTime(2020),
                        lastDate: DateTime.now(),
                      );
                      if (picked != null) {
                        setState(() {
                          paymentDate = picked;
                        });
                      }
                    },
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'Payment Date',
                        border: OutlineInputBorder(),
                      ),
                      child: Text(
                        DateFormat('MMM dd, yyyy').format(paymentDate),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  TextFormField(
                    controller: methodController,
                    decoration: const InputDecoration(
                      labelText: 'Payment Method',
                      hintText: 'Cash, Bank Transfer, etc.',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),

                  TextFormField(
                    controller: referenceController,
                    decoration: const InputDecoration(
                      labelText: 'Reference Number',
                      hintText: 'Transaction ID, Check number, etc.',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),

                  TextFormField(
                    controller: notesController,
                    decoration: const InputDecoration(
                      labelText: 'Notes',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 2,
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                final amount = double.tryParse(amountController.text) ?? 0;
                if (amount <= 0) return;

                final payment = PaymentRecord(
                  id: DateTime.now().millisecondsSinceEpoch.toString(),
                  amount: amount,
                  paymentDate: paymentDate,
                  paymentMethod: methodController.text,
                  transactionId: referenceController.text.isEmpty ? null : referenceController.text,
                  notes: notesController.text.isEmpty ? null : notesController.text,
                );

                Navigator.of(context).pop();
                context.read<InvoiceBloc>().add(
                  AddPayment(
                    invoiceId: _invoice.id,
                    payment: payment,
                  ),
                );
              },
              child: const Text('Add Payment'),
            ),
          ],
        ),
      ),
    );
  }

  void _generatePdf() async {
    setState(() {
      _isGeneratingPdf = true;
    });

    try {
      final pdfBytes = await PdfService.generateInvoicePdf(
        invoice: _invoice,
      );

      setState(() {
        _isGeneratingPdf = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('PDF generated successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      setState(() {
        _isGeneratingPdf = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error generating PDF: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showPaymentNotes(String notes) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Payment Notes'),
        content: Text(notes),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
