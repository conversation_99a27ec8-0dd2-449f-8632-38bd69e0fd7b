import 'package:equatable/equatable.dart';

class CompanyDetails extends Equatable {
  final String companyName;
  final String contactPerson;
  final String email;
  final String phone;
  final String address;
  final String? website;
  final String? gstNumber;
  final BankDetails bankDetails;
  final UpiDetails upiDetails;

  const CompanyDetails({
    required this.companyName,
    required this.contact<PERSON><PERSON>,
    required this.email,
    required this.phone,
    required this.address,
    this.website,
    this.gstNumber,
    required this.bankDetails,
    required this.upiDetails,
  });

  // Getter for backward compatibility
  String get name => companyName;

  Map<String, dynamic> toJson() {
    return {
      'companyName': companyName,
      'contactPerson': contactPerson,
      'email': email,
      'phone': phone,
      'address': address,
      'website': website,
      'gstNumber': gstNumber,
      'bankDetails': bankDetails.toJson(),
      'upiDetails': upiDetails.toJson(),
    };
  }

  factory CompanyDetails.fromJson(Map<String, dynamic> json) {
    return CompanyDetails(
      companyName: json['companyName'],
      contactPerson: json['contactPerson'],
      email: json['email'],
      phone: json['phone'],
      address: json['address'],
      website: json['website'],
      gstNumber: json['gstNumber'],
      bankDetails: BankDetails.fromJson(json['bankDetails']),
      upiDetails: UpiDetails.fromJson(json['upiDetails']),
    );
  }

  @override
  List<Object?> get props => [
        companyName,
        contactPerson,
        email,
        phone,
        address,
        website,
        gstNumber,
        bankDetails,
        upiDetails,
      ];
}

class BankDetails extends Equatable {
  final String bankName;
  final String accountNumber;
  final String ifscCode;
  final String accountHolderName;
  final String? branchName;

  const BankDetails({
    required this.bankName,
    required this.accountNumber,
    required this.ifscCode,
    required this.accountHolderName,
    this.branchName,
  });

  Map<String, dynamic> toJson() {
    return {
      'bankName': bankName,
      'accountNumber': accountNumber,
      'ifscCode': ifscCode,
      'accountHolderName': accountHolderName,
      'branchName': branchName,
    };
  }

  factory BankDetails.fromJson(Map<String, dynamic> json) {
    return BankDetails(
      bankName: json['bankName'],
      accountNumber: json['accountNumber'],
      ifscCode: json['ifscCode'],
      accountHolderName: json['accountHolderName'],
      branchName: json['branchName'],
    );
  }

  @override
  List<Object?> get props => [
        bankName,
        accountNumber,
        ifscCode,
        accountHolderName,
        branchName,
      ];
}

class UpiDetails extends Equatable {
  final String upiId;
  final String merchantName;
  final String? merchantCode;

  const UpiDetails({
    required this.upiId,
    required this.merchantName,
    this.merchantCode,
  });

  Map<String, dynamic> toJson() {
    return {
      'upiId': upiId,
      'merchantName': merchantName,
      'merchantCode': merchantCode,
    };
  }

  factory UpiDetails.fromJson(Map<String, dynamic> json) {
    return UpiDetails(
      upiId: json['upiId'],
      merchantName: json['merchantName'],
      merchantCode: json['merchantCode'],
    );
  }

  @override
  List<Object?> get props => [upiId, merchantName, merchantCode];
}

class QuotationItem extends Equatable {
  final String id;
  final String description;
  final int quantity;
  final double unitPrice;
  final double discount;
  final String unit;

  const QuotationItem({
    required this.id,
    required this.description,
    required this.quantity,
    required this.unitPrice,
    this.discount = 0.0,
    this.unit = 'pcs',
  });

  double get totalPrice => (quantity * unitPrice) - discount;
  double get subtotal => quantity * unitPrice;

  QuotationItem copyWith({
    String? id,
    String? description,
    int? quantity,
    double? unitPrice,
    double? discount,
    String? unit,
  }) {
    return QuotationItem(
      id: id ?? this.id,
      description: description ?? this.description,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      discount: discount ?? this.discount,
      unit: unit ?? this.unit,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'description': description,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'discount': discount,
      'unit': unit,
    };
  }

  factory QuotationItem.fromJson(Map<String, dynamic> json) {
    return QuotationItem(
      id: json['id'],
      description: json['description'],
      quantity: json['quantity'],
      unitPrice: json['unitPrice'].toDouble(),
      discount: json['discount'].toDouble(),
      unit: json['unit'],
    );
  }

  @override
  List<Object?> get props =>
      [id, description, quantity, unitPrice, discount, unit];
}

class Client extends Equatable {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String address;
  final String company;

  const Client({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.address,
    this.company = '',
  });

  Client copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? address,
    String? company,
  }) {
    return Client(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      company: company ?? this.company,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'address': address,
      'company': company,
    };
  }

  factory Client.fromJson(Map<String, dynamic> json) {
    return Client(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      phone: json['phone'],
      address: json['address'],
      company: json['company'] ?? '',
    );
  }

  @override
  List<Object?> get props => [id, name, email, phone, address, company];
}

enum QuotationStatus {
  draft,
  sent,
  accepted,
  rejected,
  expired,
}

class Quotation extends Equatable {
  final String id;
  final String quotationNumber;
  final Client client;
  final DateTime createdDate;
  final DateTime validUntil;
  final List<QuotationItem> items;
  final double taxRate;
  final double shippingCost;
  final String notes;
  final QuotationStatus status;
  final String createdBy;

  const Quotation({
    required this.id,
    required this.quotationNumber,
    required this.client,
    required this.createdDate,
    required this.validUntil,
    required this.items,
    this.taxRate = 0.0,
    this.shippingCost = 0.0,
    this.notes = '',
    this.status = QuotationStatus.draft,
    this.createdBy = 'Admin User',
  });

  double get subtotal => items.fold(0.0, (sum, item) => sum + item.subtotal);
  double get totalDiscount =>
      items.fold(0.0, (sum, item) => sum + item.discount);
  double get taxAmount => (subtotal - totalDiscount) * (taxRate / 100);
  double get totalAmount => subtotal - totalDiscount + taxAmount + shippingCost;

  bool get isExpired => DateTime.now().isAfter(validUntil);

  String get statusText {
    switch (status) {
      case QuotationStatus.draft:
        return 'Draft';
      case QuotationStatus.sent:
        return 'Sent';
      case QuotationStatus.accepted:
        return 'Accepted';
      case QuotationStatus.rejected:
        return 'Rejected';
      case QuotationStatus.expired:
        return 'Expired';
    }
  }

  Quotation copyWith({
    String? id,
    String? quotationNumber,
    Client? client,
    DateTime? createdDate,
    DateTime? validUntil,
    List<QuotationItem>? items,
    double? taxRate,
    double? shippingCost,
    String? notes,
    QuotationStatus? status,
    String? createdBy,
  }) {
    return Quotation(
      id: id ?? this.id,
      quotationNumber: quotationNumber ?? this.quotationNumber,
      client: client ?? this.client,
      createdDate: createdDate ?? this.createdDate,
      validUntil: validUntil ?? this.validUntil,
      items: items ?? this.items,
      taxRate: taxRate ?? this.taxRate,
      shippingCost: shippingCost ?? this.shippingCost,
      notes: notes ?? this.notes,
      status: status ?? this.status,
      createdBy: createdBy ?? this.createdBy,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'quotationNumber': quotationNumber,
      'client': client.toJson(),
      'createdDate': createdDate.toIso8601String(),
      'validUntil': validUntil.toIso8601String(),
      'items': items.map((item) => item.toJson()).toList(),
      'taxRate': taxRate,
      'shippingCost': shippingCost,
      'notes': notes,
      'status': status.name,
      'createdBy': createdBy,
    };
  }

  factory Quotation.fromJson(Map<String, dynamic> json) {
    return Quotation(
      id: json['id'],
      quotationNumber: json['quotationNumber'],
      client: Client.fromJson(json['client']),
      createdDate: DateTime.parse(json['createdDate']),
      validUntil: DateTime.parse(json['validUntil']),
      items: (json['items'] as List)
          .map((item) => QuotationItem.fromJson(item))
          .toList(),
      taxRate: json['taxRate'].toDouble(),
      shippingCost: json['shippingCost'].toDouble(),
      notes: json['notes'],
      status: QuotationStatus.values.firstWhere(
        (status) => status.name == json['status'],
        orElse: () => QuotationStatus.draft,
      ),
      createdBy: json['createdBy'],
    );
  }

  @override
  List<Object?> get props => [
        id,
        quotationNumber,
        client,
        createdDate,
        validUntil,
        items,
        taxRate,
        shippingCost,
        notes,
        status,
        createdBy,
      ];
}
