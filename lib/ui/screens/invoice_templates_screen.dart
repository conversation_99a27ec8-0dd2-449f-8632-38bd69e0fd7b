import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../models/invoice_models.dart';
import '../../services/pdf_service.dart';
import '../widgets/loading_widget.dart';

class InvoiceTemplatesScreen extends StatefulWidget {
  final InvoiceTemplate? selectedTemplate;
  final Function(InvoiceTemplate)? onTemplateSelected;

  const InvoiceTemplatesScreen({
    super.key,
    this.selectedTemplate,
    this.onTemplateSelected,
  });

  @override
  State<InvoiceTemplatesScreen> createState() => _InvoiceTemplatesScreenState();
}

class _InvoiceTemplatesScreenState extends State<InvoiceTemplatesScreen> {
  InvoiceTemplate? _selectedTemplate;
  bool _isGeneratingPreview = false;

  @override
  void initState() {
    super.initState();
    _selectedTemplate = widget.selectedTemplate ?? InvoiceTemplate.modern;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Invoice Templates'),
        actions: [
          if (widget.onTemplateSelected != null)
            TextButton(
              onPressed: _selectedTemplate != null
                  ? () {
                      widget.onTemplateSelected!(_selectedTemplate!);
                      Navigator.of(context).pop();
                    }
                  : null,
              child: const Text('Select'),
            ),
        ],
      ),
      body: LoadingOverlay(
        isLoading: _isGeneratingPreview,
        loadingMessage: 'Generating preview...',
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Choose a template for your invoices',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Select a template that best represents your business style',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 24),
              
              Expanded(
                child: GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    childAspectRatio: 0.7,
                  ),
                  itemCount: InvoiceTemplate.values.length,
                  itemBuilder: (context, index) {
                    final template = InvoiceTemplate.values[index];
                    return _buildTemplateCard(template);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTemplateCard(InvoiceTemplate template) {
    final isSelected = _selectedTemplate == template;
    final templateInfo = _getTemplateInfo(template);
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedTemplate = template;
        });
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected 
                ? Theme.of(context).primaryColor 
                : Colors.grey.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: Theme.of(context).primaryColor.withOpacity(0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ]
              : [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
        ),
        child: Column(
          children: [
            // Template Preview
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: templateInfo.backgroundColor,
                  borderRadius: BorderRadius.circular(8),
                  gradient: templateInfo.gradient,
                ),
                child: Stack(
                  children: [
                    // Mock invoice content
                    Padding(
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header
                          Row(
                            children: [
                              Container(
                                width: 30,
                                height: 30,
                                decoration: BoxDecoration(
                                  color: templateInfo.accentColor,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      height: 8,
                                      width: 60,
                                      color: templateInfo.textColor,
                                    ),
                                    const SizedBox(height: 2),
                                    Container(
                                      height: 6,
                                      width: 40,
                                      color: templateInfo.textColor.withOpacity(0.6),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          
                          // Invoice details
                          Container(
                            height: 6,
                            width: 80,
                            color: templateInfo.textColor,
                          ),
                          const SizedBox(height: 4),
                          Container(
                            height: 4,
                            width: 60,
                            color: templateInfo.textColor.withOpacity(0.6),
                          ),
                          const SizedBox(height: 12),
                          
                          // Items
                          ...List.generate(3, (index) => Padding(
                            padding: const EdgeInsets.only(bottom: 6),
                            child: Row(
                              children: [
                                Container(
                                  height: 4,
                                  width: 40,
                                  color: templateInfo.textColor.withOpacity(0.8),
                                ),
                                const Spacer(),
                                Container(
                                  height: 4,
                                  width: 20,
                                  color: templateInfo.textColor.withOpacity(0.8),
                                ),
                              ],
                            ),
                          )),
                          
                          const Spacer(),
                          
                          // Total
                          Container(
                            height: 1,
                            color: templateInfo.textColor.withOpacity(0.3),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Container(
                                height: 6,
                                width: 30,
                                color: templateInfo.textColor,
                              ),
                              const Spacer(),
                              Container(
                                height: 6,
                                width: 25,
                                color: templateInfo.accentColor,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    
                    // Selection indicator
                    if (isSelected)
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            
            // Template Info
            Expanded(
              flex: 1,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      templateInfo.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      templateInfo.description,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  TemplateInfo _getTemplateInfo(InvoiceTemplate template) {
    switch (template) {
      case InvoiceTemplate.modern:
        return TemplateInfo(
          name: 'Modern',
          description: 'Clean design with gradients and modern styling',
          backgroundColor: Colors.white,
          textColor: Colors.black87,
          accentColor: Colors.blue,
          gradient: LinearGradient(
            colors: [Colors.blue.withOpacity(0.1), Colors.white],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        );
      case InvoiceTemplate.classic:
        return TemplateInfo(
          name: 'Classic',
          description: 'Traditional business invoice with borders',
          backgroundColor: Colors.white,
          textColor: Colors.black87,
          accentColor: Colors.grey[800]!,
        );
      case InvoiceTemplate.minimal:
        return TemplateInfo(
          name: 'Minimal',
          description: 'Simple and clean design with minimal elements',
          backgroundColor: Colors.grey[50]!,
          textColor: Colors.black87,
          accentColor: Colors.grey[600]!,
        );
      case InvoiceTemplate.professional:
        return TemplateInfo(
          name: 'Professional',
          description: 'Corporate style with professional appearance',
          backgroundColor: Colors.white,
          textColor: Colors.black87,
          accentColor: Colors.indigo,
          gradient: LinearGradient(
            colors: [Colors.indigo.withOpacity(0.05), Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        );
    }
  }
}

class TemplateInfo {
  final String name;
  final String description;
  final Color backgroundColor;
  final Color textColor;
  final Color accentColor;
  final Gradient? gradient;

  TemplateInfo({
    required this.name,
    required this.description,
    required this.backgroundColor,
    required this.textColor,
    required this.accentColor,
    this.gradient,
  });
}
