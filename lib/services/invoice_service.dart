import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:excel/excel.dart';
import 'package:intl/intl.dart';

import '../models/invoice_models.dart';
import '../models/quotation_models.dart';
import 'file_service.dart';
import 'payment_service.dart';

class InvoiceService {
  static InvoiceService? _instance;
  static InvoiceService get instance => _instance ??= InvoiceService._();
  InvoiceService._();

  SharedPreferences? _prefs;
  static const String _keyInvoiceCounter = 'invoice_counter';
  static const String _keyInvoices = 'invoices';

  Future<void> _initPrefs() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  // Generate invoice number
  Future<String> generateInvoiceNumber() async {
    await _initPrefs();
    final counter = _prefs!.getInt(_keyInvoiceCounter) ?? 0;
    final newCounter = counter + 1;
    await _prefs!.setInt(_keyInvoiceCounter, newCounter);

    final dateFormat = DateFormat('yyyyMM');
    final dateString = dateFormat.format(DateTime.now());
    return 'INV-$dateString-${newCounter.toString().padLeft(4, '0')}';
  }

  // Create invoice from quotation
  Future<Invoice> createInvoiceFromQuotation(
    Quotation quotation, {
    DateTime? issueDate,
    DateTime? dueDate,
    InvoiceTemplate template = InvoiceTemplate.modern,
  }) async {
    final invoiceNumber = await generateInvoiceNumber();
    final now = DateTime.now();
    
    return Invoice.fromQuotation(
      quotation,
      invoiceNumber: invoiceNumber,
      issueDate: issueDate ?? now,
      dueDate: dueDate ?? now.add(const Duration(days: 30)),
      template: template,
    );
  }

  // Save invoice locally
  Future<void> saveInvoice(Invoice invoice) async {
    await _initPrefs();
    final invoices = await getInvoices();
    final existingIndex = invoices.indexWhere((inv) => inv.id == invoice.id);
    
    if (existingIndex >= 0) {
      invoices[existingIndex] = invoice.copyWith(updatedAt: DateTime.now());
    } else {
      invoices.add(invoice);
    }
    
    final invoicesJson = invoices.map((inv) => inv.toJson()).toList();
    await _prefs!.setString(_keyInvoices, jsonEncode(invoicesJson));
  }

  // Get all invoices
  Future<List<Invoice>> getInvoices() async {
    await _initPrefs();
    final invoicesString = _prefs!.getString(_keyInvoices);
    if (invoicesString == null) return [];
    
    final invoicesJson = jsonDecode(invoicesString) as List;
    return invoicesJson
        .map((json) => Invoice.fromJson(json as Map<String, dynamic>))
        .toList();
  }

  // Get invoice by ID
  Future<Invoice?> getInvoiceById(String id) async {
    final invoices = await getInvoices();
    try {
      return invoices.firstWhere((invoice) => invoice.id == id);
    } catch (e) {
      return null;
    }
  }

  // Delete invoice
  Future<void> deleteInvoice(String id) async {
    await _initPrefs();
    final invoices = await getInvoices();
    invoices.removeWhere((invoice) => invoice.id == id);
    
    final invoicesJson = invoices.map((inv) => inv.toJson()).toList();
    await _prefs!.setString(_keyInvoices, jsonEncode(invoicesJson));
  }

  // Update invoice status
  Future<void> updateInvoiceStatus(String id, InvoiceStatus status) async {
    final invoice = await getInvoiceById(id);
    if (invoice != null) {
      final updatedInvoice = invoice.copyWith(
        status: status,
        updatedAt: DateTime.now(),
      );
      await saveInvoice(updatedInvoice);
    }
  }

  // Update payment status
  Future<void> updatePaymentStatus(String id, PaymentStatus paymentStatus) async {
    final invoice = await getInvoiceById(id);
    if (invoice != null) {
      final updatedInvoice = invoice.copyWith(
        paymentStatus: paymentStatus,
        updatedAt: DateTime.now(),
      );
      await saveInvoice(updatedInvoice);
    }
  }

  // Add payment record
  Future<void> addPayment(String invoiceId, PaymentRecord payment) async {
    final invoice = await getInvoiceById(invoiceId);
    if (invoice != null) {
      final updatedPayments = List<PaymentRecord>.from(invoice.payments)..add(payment);
      
      // Update payment status based on total paid amount
      final totalPaid = updatedPayments.fold(0.0, (sum, p) => sum + p.amount);
      PaymentStatus newPaymentStatus;
      
      if (totalPaid >= invoice.totalAmount) {
        newPaymentStatus = PaymentStatus.paid;
      } else if (totalPaid > 0) {
        newPaymentStatus = PaymentStatus.partial;
      } else {
        newPaymentStatus = PaymentStatus.pending;
      }
      
      final updatedInvoice = invoice.copyWith(
        payments: updatedPayments,
        paymentStatus: newPaymentStatus,
        status: newPaymentStatus == PaymentStatus.paid ? InvoiceStatus.paid : invoice.status,
        updatedAt: DateTime.now(),
      );
      
      await saveInvoice(updatedInvoice);
    }
  }

  // Search invoices
  Future<List<Invoice>> searchInvoices({
    String? query,
    InvoiceStatus? status,
    PaymentStatus? paymentStatus,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final invoices = await getInvoices();
    
    return invoices.where((invoice) {
      // Text search
      if (query != null && query.isNotEmpty) {
        final searchText = query.toLowerCase();
        final matchesNumber = invoice.invoiceNumber.toLowerCase().contains(searchText);
        final matchesClient = invoice.client.name.toLowerCase().contains(searchText);
        final matchesEmail = invoice.client.email.toLowerCase().contains(searchText);
        
        if (!matchesNumber && !matchesClient && !matchesEmail) {
          return false;
        }
      }
      
      // Status filter
      if (status != null && invoice.status != status) {
        return false;
      }
      
      // Payment status filter
      if (paymentStatus != null && invoice.paymentStatus != paymentStatus) {
        return false;
      }
      
      // Date range filter
      if (startDate != null && invoice.issueDate.isBefore(startDate)) {
        return false;
      }
      
      if (endDate != null && invoice.issueDate.isAfter(endDate)) {
        return false;
      }
      
      return true;
    }).toList();
  }

  // Get overdue invoices
  Future<List<Invoice>> getOverdueInvoices() async {
    final invoices = await getInvoices();
    return invoices.where((invoice) => invoice.isOverdue).toList();
  }

  // Get invoice statistics
  Future<Map<String, dynamic>> getInvoiceStatistics() async {
    final invoices = await getInvoices();
    
    final totalInvoices = invoices.length;
    final paidInvoices = invoices.where((inv) => inv.isPaid).length;
    final overdueInvoices = invoices.where((inv) => inv.isOverdue).length;
    final draftInvoices = invoices.where((inv) => inv.status == InvoiceStatus.draft).length;
    
    final totalAmount = invoices.fold(0.0, (sum, inv) => sum + inv.totalAmount);
    final paidAmount = invoices.fold(0.0, (sum, inv) => sum + inv.paidAmount);
    final pendingAmount = totalAmount - paidAmount;
    
    return {
      'totalInvoices': totalInvoices,
      'paidInvoices': paidInvoices,
      'overdueInvoices': overdueInvoices,
      'draftInvoices': draftInvoices,
      'totalAmount': totalAmount,
      'paidAmount': paidAmount,
      'pendingAmount': pendingAmount,
    };
  }

  // Mark invoice as sent
  Future<void> markAsSent(String id) async {
    await updateInvoiceStatus(id, InvoiceStatus.sent);
  }

  // Mark invoice as paid
  Future<void> markAsPaid(String id, {PaymentRecord? paymentRecord}) async {
    final invoice = await getInvoiceById(id);
    if (invoice != null) {
      if (paymentRecord != null) {
        await addPayment(id, paymentRecord);
      } else {
        await updateInvoiceStatus(id, InvoiceStatus.paid);
        await updatePaymentStatus(id, PaymentStatus.paid);
      }
    }
  }

  // Cancel invoice
  Future<void> cancelInvoice(String id) async {
    await updateInvoiceStatus(id, InvoiceStatus.cancelled);
    await updatePaymentStatus(id, PaymentStatus.cancelled);
  }
}
