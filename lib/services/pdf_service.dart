import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:qr/qr.dart';

import '../models/bank_models.dart';
import '../models/quotation_models.dart';
import '../models/invoice_models.dart';
import 'payment_service.dart';

class PdfService {
  static Future<File> generateQuotationPdf({
    required Quotation quotation,
    BankAccount? bankAccount,
    UpiAccount? upiAccount,
    bool showBankDetails = true,
    bool showUpiDetails = true,
    bool showQrCode = true,
    String? paymentInstructions,
  }) async {
    final pdf = pw.Document();
    final dateFormat = DateFormat('MMM dd, yyyy');
    final currencyFormat = NumberFormat.currency(symbol: '₹');

    // Generate QR code if needed
    pw.Widget? qrCodeWidget;
    if (showQrCode && upiAccount != null) {
      qrCodeWidget = await _generateQrCodeWidget(quotation, upiAccount);
    }

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(40),
        build: (context) {
          return [
            // Header
            _buildHeader(quotation, dateFormat),
            pw.SizedBox(height: 20),
            
            // Client Details
            _buildClientDetails(quotation),
            pw.SizedBox(height: 20),
            
            // Items Table
            _buildItemsTable(quotation, currencyFormat),
            pw.SizedBox(height: 20),
            
            // Total Summary
            _buildTotalSummary(quotation, currencyFormat),
            pw.SizedBox(height: 20),
            
            // Payment Information
            if (showBankDetails || showUpiDetails || showQrCode)
              _buildPaymentSection(
                bankAccount: bankAccount,
                upiAccount: upiAccount,
                showBankDetails: showBankDetails,
                showUpiDetails: showUpiDetails,
                qrCodeWidget: qrCodeWidget,
                paymentInstructions: paymentInstructions,
                totalAmount: quotation.totalAmount,
              ),
            
            // Notes
            if (quotation.notes.isNotEmpty) ...[
              pw.SizedBox(height: 20),
              _buildNotes(quotation),
            ],
            
            // Footer
            pw.SizedBox(height: 30),
            _buildFooter(),
          ];
        },
      ),
    );

    // Save PDF
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/quotation_${quotation.quotationNumber}.pdf');
    await file.writeAsBytes(await pdf.save());
    
    return file;
  }

  // =================== INVOICE PDF GENERATION ===================

  static Future<Uint8List> generateInvoicePdf({
    required Invoice invoice,
    BankAccount? bankAccount,
    UpiAccount? upiAccount,
    bool showBankDetails = true,
    bool showUpiDetails = true,
    bool showQrCode = true,
    String? paymentInstructions,
  }) async {
    switch (invoice.template) {
      case InvoiceTemplate.modern:
        return _generateModernInvoicePdf(
          invoice: invoice,
          bankAccount: bankAccount,
          upiAccount: upiAccount,
          showBankDetails: showBankDetails,
          showUpiDetails: showUpiDetails,
          showQrCode: showQrCode,
          paymentInstructions: paymentInstructions,
        );
      case InvoiceTemplate.classic:
        return _generateClassicInvoicePdf(
          invoice: invoice,
          bankAccount: bankAccount,
          upiAccount: upiAccount,
          showBankDetails: showBankDetails,
          showUpiDetails: showUpiDetails,
          showQrCode: showQrCode,
          paymentInstructions: paymentInstructions,
        );
      case InvoiceTemplate.minimal:
        return _generateMinimalInvoicePdf(
          invoice: invoice,
          bankAccount: bankAccount,
          upiAccount: upiAccount,
          showBankDetails: showBankDetails,
          showUpiDetails: showUpiDetails,
          showQrCode: showQrCode,
          paymentInstructions: paymentInstructions,
        );
      case InvoiceTemplate.professional:
        return _generateProfessionalInvoicePdf(
          invoice: invoice,
          bankAccount: bankAccount,
          upiAccount: upiAccount,
          showBankDetails: showBankDetails,
          showUpiDetails: showUpiDetails,
          showQrCode: showQrCode,
          paymentInstructions: paymentInstructions,
        );
    }
  }

  // =================== MODERN INVOICE TEMPLATE ===================

  static Future<File> _generateModernInvoicePdf({
    required Invoice invoice,
    BankAccount? bankAccount,
    UpiAccount? upiAccount,
    bool showBankDetails = true,
    bool showUpiDetails = true,
    bool showQrCode = true,
    String? paymentInstructions,
  }) async {
    final pdf = pw.Document();
    final dateFormat = DateFormat('MMM dd, yyyy');
    final currencyFormat = NumberFormat.currency(symbol: '₹');

    // Generate QR code if needed
    pw.Widget? qrCodeWidget;
    if (showQrCode && upiAccount != null) {
      qrCodeWidget = await _generateInvoiceQrCodeWidget(invoice, upiAccount);
    }

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(40),
        build: (context) {
          return [
            // Modern Header with gradient-like effect
            _buildModernInvoiceHeader(invoice, dateFormat),
            pw.SizedBox(height: 30),

            // Invoice Status Badge
            _buildInvoiceStatusBadge(invoice),
            pw.SizedBox(height: 20),

            // Client Details with modern styling
            _buildModernClientDetails(invoice),
            pw.SizedBox(height: 30),

            // Items Table with modern design
            _buildModernInvoiceItemsTable(invoice, currencyFormat),
            pw.SizedBox(height: 30),

            // Total Summary with modern styling
            _buildModernInvoiceTotalSummary(invoice, currencyFormat),
            pw.SizedBox(height: 30),

            // Payment Information
            if (showBankDetails || showUpiDetails || showQrCode)
              _buildModernPaymentSection(
                bankAccount: bankAccount,
                upiAccount: upiAccount,
                showBankDetails: showBankDetails,
                showUpiDetails: showUpiDetails,
                qrCodeWidget: qrCodeWidget,
                paymentInstructions: paymentInstructions,
                totalAmount: invoice.totalAmount,
              ),

            // Payment History if any
            if (invoice.payments.isNotEmpty) ...[
              pw.SizedBox(height: 30),
              _buildPaymentHistory(invoice, currencyFormat),
            ],

            // Notes
            if (invoice.notes.isNotEmpty) ...[
              pw.SizedBox(height: 30),
              _buildModernNotes(invoice),
            ],

            // Terms and Conditions
            if (invoice.termsAndConditions.isNotEmpty) ...[
              pw.SizedBox(height: 20),
              _buildModernTermsAndConditions(invoice),
            ],

            // Modern Footer
            pw.SizedBox(height: 40),
            _buildModernInvoiceFooter(invoice),
          ];
        },
      ),
    );

    // Save PDF
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/invoice_${invoice.invoiceNumber}.pdf');
    await file.writeAsBytes(await pdf.save());

    return file;
  }

  // =================== MODERN INVOICE HELPER METHODS ===================

  static pw.Widget _buildModernInvoiceHeader(Invoice invoice, DateFormat dateFormat) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(24),
      decoration: pw.BoxDecoration(
        gradient: const pw.LinearGradient(
          colors: [PdfColors.blue800, PdfColors.blue600],
          begin: pw.Alignment.topLeft,
          end: pw.Alignment.bottomRight,
        ),
        borderRadius: pw.BorderRadius.circular(12),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                invoice.companyDetails?.name ?? 'AN ENTERPRISES',
                style: pw.TextStyle(
                  fontSize: 28,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.white,
                ),
              ),
              pw.SizedBox(height: 8),
              pw.Text(
                'INVOICE',
                style: pw.TextStyle(
                  fontSize: 16,
                  fontWeight: pw.FontWeight.normal,
                  color: PdfColors.white,
                  letterSpacing: 2,
                ),
              ),
            ],
          ),
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              pw.Text(
                'Invoice #${invoice.invoiceNumber}',
                style: pw.TextStyle(
                  fontSize: 18,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.white,
                ),
              ),
              pw.SizedBox(height: 8),
              pw.Text(
                'Issue Date: ${dateFormat.format(invoice.issueDate)}',
                style: const pw.TextStyle(
                  fontSize: 12,
                  color: PdfColors.white,
                ),
              ),
              pw.Text(
                'Due Date: ${dateFormat.format(invoice.dueDate)}',
                style: const pw.TextStyle(
                  fontSize: 12,
                  color: PdfColors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildInvoiceStatusBadge(Invoice invoice) {
    PdfColor badgeColor;
    String statusText;

    switch (invoice.status) {
      case InvoiceStatus.draft:
        badgeColor = PdfColors.grey600;
        statusText = 'DRAFT';
        break;
      case InvoiceStatus.sent:
        badgeColor = PdfColors.blue600;
        statusText = 'SENT';
        break;
      case InvoiceStatus.viewed:
        badgeColor = PdfColors.orange600;
        statusText = 'VIEWED';
        break;
      case InvoiceStatus.paid:
        badgeColor = PdfColors.green600;
        statusText = 'PAID';
        break;
      case InvoiceStatus.overdue:
        badgeColor = PdfColors.red600;
        statusText = 'OVERDUE';
        break;
      case InvoiceStatus.cancelled:
        badgeColor = PdfColors.red800;
        statusText = 'CANCELLED';
        break;
    }

    return pw.Row(
      children: [
        pw.Container(
          padding: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: pw.BoxDecoration(
            color: badgeColor,
            borderRadius: pw.BorderRadius.circular(20),
          ),
          child: pw.Text(
            statusText,
            style: pw.TextStyle(
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.white,
              letterSpacing: 1,
            ),
          ),
        ),
        pw.Spacer(),
        if (invoice.paymentStatus != PaymentStatus.pending)
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: pw.BoxDecoration(
              color: invoice.paymentStatus == PaymentStatus.paid
                  ? PdfColors.green600
                  : invoice.paymentStatus == PaymentStatus.partial
                      ? PdfColors.orange600
                      : PdfColors.red600,
              borderRadius: pw.BorderRadius.circular(20),
            ),
            child: pw.Text(
              invoice.paymentStatus.toString().split('.').last.toUpperCase(),
              style: pw.TextStyle(
                fontSize: 12,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.white,
                letterSpacing: 1,
              ),
            ),
          ),
      ],
    );
  }

  static pw.Widget _buildModernClientDetails(Invoice invoice) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey50,
        borderRadius: pw.BorderRadius.circular(12),
        border: pw.Border.all(color: PdfColors.grey200),
      ),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Expanded(
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'Bill To:',
                  style: pw.TextStyle(
                    fontSize: 14,
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.grey700,
                  ),
                ),
                pw.SizedBox(height: 8),
                pw.Text(
                  invoice.client.name,
                  style: pw.TextStyle(
                    fontSize: 16,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.SizedBox(height: 4),
                pw.Text(invoice.client.email),
                if (invoice.client.phone.isNotEmpty) pw.Text(invoice.client.phone),
                if (invoice.client.address.isNotEmpty) ...[
                  pw.SizedBox(height: 4),
                  pw.Text(invoice.client.address),
                ],
              ],
            ),
          ),
          pw.SizedBox(width: 40),
          pw.Expanded(
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'From:',
                  style: pw.TextStyle(
                    fontSize: 14,
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.grey700,
                  ),
                ),
                pw.SizedBox(height: 8),
                pw.Text(
                  invoice.companyDetails?.name ?? 'AN ENTERPRISES',
                  style: pw.TextStyle(
                    fontSize: 16,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                if (invoice.companyDetails?.email != null) ...[
                  pw.SizedBox(height: 4),
                  pw.Text(invoice.companyDetails!.email),
                ],
                if (invoice.companyDetails?.phone != null) ...[
                  pw.Text(invoice.companyDetails!.phone),
                ],
                if (invoice.companyDetails?.address != null) ...[
                  pw.SizedBox(height: 4),
                  pw.Text(invoice.companyDetails!.address),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildModernInvoiceItemsTable(Invoice invoice, NumberFormat currencyFormat) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        borderRadius: pw.BorderRadius.circular(12),
        border: pw.Border.all(color: PdfColors.grey200),
      ),
      child: pw.Column(
        children: [
          // Header
          pw.Container(
            padding: const pw.EdgeInsets.all(16),
            decoration: pw.BoxDecoration(
              gradient: const pw.LinearGradient(
                colors: [PdfColors.blue50, PdfColors.blue100],
              ),
              borderRadius: const pw.BorderRadius.only(
                topLeft: pw.Radius.circular(12),
                topRight: pw.Radius.circular(12),
              ),
            ),
            child: pw.Text(
              'Invoice Items',
              style: pw.TextStyle(
                fontSize: 18,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.blue800,
              ),
            ),
          ),
          // Table
          pw.Table(
            border: pw.TableBorder.symmetric(
              inside: const pw.BorderSide(color: PdfColors.grey200),
            ),
            columnWidths: {
              0: const pw.FlexColumnWidth(3),
              1: const pw.FlexColumnWidth(1),
              2: const pw.FlexColumnWidth(1),
              3: const pw.FlexColumnWidth(1.5),
              4: const pw.FlexColumnWidth(1.5),
              5: const pw.FlexColumnWidth(2),
            },
            children: [
              // Header row
              pw.TableRow(
                decoration: const pw.BoxDecoration(color: PdfColors.grey50),
                children: [
                  _buildModernTableHeader('Description'),
                  _buildModernTableHeader('Qty'),
                  _buildModernTableHeader('Unit'),
                  _buildModernTableHeader('Rate'),
                  _buildModernTableHeader('Discount'),
                  _buildModernTableHeader('Amount'),
                ],
              ),
              // Items
              ...invoice.items.map((item) {
                return pw.TableRow(
                  children: [
                    _buildModernTableCell(item.description),
                    _buildModernTableCell(item.quantity.toString(), alignment: pw.Alignment.center),
                    _buildModernTableCell(item.unit ?? '', alignment: pw.Alignment.center),
                    _buildModernTableCell(currencyFormat.format(item.unitPrice), alignment: pw.Alignment.centerRight),
                    _buildModernTableCell(currencyFormat.format(item.discount), alignment: pw.Alignment.centerRight),
                    _buildModernTableCell(
                      currencyFormat.format(item.total),
                      alignment: pw.Alignment.centerRight,
                      isBold: true,
                      textColor: PdfColors.blue800,
                    ),
                  ],
                );
              }),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildModernTableHeader(String text) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: 12,
          fontWeight: pw.FontWeight.bold,
          color: PdfColors.grey700,
        ),
      ),
    );
  }

  static pw.Widget _buildModernTableCell(
    String text, {
    pw.Alignment alignment = pw.Alignment.centerLeft,
    bool isBold = false,
    PdfColor textColor = PdfColors.black,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      alignment: alignment,
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: 11,
          fontWeight: isBold ? pw.FontWeight.bold : pw.FontWeight.normal,
          color: textColor,
        ),
      ),
    );
  }

  static pw.Widget _buildModernInvoiceTotalSummary(Invoice invoice, NumberFormat currencyFormat) {
    final subtotal = invoice.subtotalAmount;
    final taxAmount = invoice.taxAmount;
    final total = invoice.totalAmount;

    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.end,
      children: [
        pw.Container(
          width: 300,
          decoration: pw.BoxDecoration(
            borderRadius: pw.BorderRadius.circular(12),
            border: pw.Border.all(color: PdfColors.grey200),
          ),
          child: pw.Column(
            children: [
              // Subtotal
              _buildModernSummaryRow('Subtotal', currencyFormat.format(subtotal)),

              // Tax
              if (invoice.taxRate > 0)
                _buildModernSummaryRow(
                  'Tax (${invoice.taxRate.toStringAsFixed(1)}%)',
                  currencyFormat.format(taxAmount),
                ),

              // Shipping
              if (invoice.shippingCost > 0)
                _buildModernSummaryRow('Shipping', currencyFormat.format(invoice.shippingCost)),

              // Additional charges
              if (invoice.additionalCharges > 0)
                _buildModernSummaryRow('Additional Charges', currencyFormat.format(invoice.additionalCharges)),

              // Total
              pw.Container(
                padding: const pw.EdgeInsets.all(16),
                decoration: pw.BoxDecoration(
                  gradient: const pw.LinearGradient(
                    colors: [PdfColors.blue600, PdfColors.blue800],
                  ),
                  borderRadius: const pw.BorderRadius.only(
                    bottomLeft: pw.Radius.circular(12),
                    bottomRight: pw.Radius.circular(12),
                  ),
                ),
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text(
                      'Total Amount',
                      style: pw.TextStyle(
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.white,
                      ),
                    ),
                    pw.Text(
                      currencyFormat.format(total),
                      style: pw.TextStyle(
                        fontSize: 18,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  static pw.Widget _buildModernSummaryRow(String label, String value) {
    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: const pw.BoxDecoration(
        border: pw.Border(bottom: pw.BorderSide(color: PdfColors.grey200)),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: const pw.TextStyle(fontSize: 12),
          ),
          pw.Text(
            value,
            style: pw.TextStyle(
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildModernPaymentSection({
    BankAccount? bankAccount,
    UpiAccount? upiAccount,
    bool showBankDetails = true,
    bool showUpiDetails = true,
    pw.Widget? qrCodeWidget,
    String? paymentInstructions,
    required double totalAmount,
  }) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        borderRadius: pw.BorderRadius.circular(12),
        border: pw.Border.all(color: PdfColors.grey200),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // Header
          pw.Container(
            padding: const pw.EdgeInsets.all(16),
            decoration: pw.BoxDecoration(
              gradient: const pw.LinearGradient(
                colors: [PdfColors.green50, PdfColors.green100],
              ),
              borderRadius: const pw.BorderRadius.only(
                topLeft: pw.Radius.circular(12),
                topRight: pw.Radius.circular(12),
              ),
            ),
            child: pw.Text(
              'Payment Information',
              style: pw.TextStyle(
                fontSize: 18,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.green800,
              ),
            ),
          ),

          // Content
          pw.Container(
            padding: const pw.EdgeInsets.all(16),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                if (paymentInstructions != null) ...[
                  pw.Text(
                    paymentInstructions,
                    style: const pw.TextStyle(fontSize: 12),
                  ),
                  pw.SizedBox(height: 16),
                ],

                pw.Row(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    // Bank Details
                    if (showBankDetails && bankAccount != null) ...[
                      pw.Expanded(
                        child: _buildModernBankDetailsSection(bankAccount),
                      ),
                      pw.SizedBox(width: 16),
                    ],

                    // UPI Details
                    if (showUpiDetails && upiAccount != null) ...[
                      pw.Expanded(
                        child: _buildModernUpiDetailsSection(upiAccount),
                      ),
                      if (qrCodeWidget != null) pw.SizedBox(width: 16),
                    ],

                    // QR Code
                    if (qrCodeWidget != null) ...[
                      _buildModernQrCodeSection(qrCodeWidget, totalAmount),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildPaymentHistory(Invoice invoice, NumberFormat currencyFormat) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        borderRadius: pw.BorderRadius.circular(12),
        border: pw.Border.all(color: PdfColors.grey200),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // Header
          pw.Container(
            padding: const pw.EdgeInsets.all(16),
            decoration: pw.BoxDecoration(
              gradient: const pw.LinearGradient(
                colors: [PdfColors.purple50, PdfColors.purple100],
              ),
              borderRadius: const pw.BorderRadius.only(
                topLeft: pw.Radius.circular(12),
                topRight: pw.Radius.circular(12),
              ),
            ),
            child: pw.Text(
              'Payment History',
              style: pw.TextStyle(
                fontSize: 18,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.purple800,
              ),
            ),
          ),

          // Payment records
          pw.Container(
            padding: const pw.EdgeInsets.all(16),
            child: pw.Column(
              children: invoice.payments.map((payment) {
                return pw.Container(
                  margin: const pw.EdgeInsets.only(bottom: 8),
                  padding: const pw.EdgeInsets.all(12),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.grey50,
                    borderRadius: pw.BorderRadius.circular(8),
                  ),
                  child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                            DateFormat('MMM dd, yyyy').format(payment.paymentDate),
                            style: pw.TextStyle(
                              fontSize: 12,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.Text(
                            payment.paymentMethod,
                            style: const pw.TextStyle(fontSize: 10),
                          ),
                          if (payment.reference?.isNotEmpty == true)
                            pw.Text(
                              'Ref: ${payment.reference}',
                              style: const pw.TextStyle(fontSize: 10),
                            ),
                        ],
                      ),
                      pw.Text(
                        currencyFormat.format(payment.amount),
                        style: pw.TextStyle(
                          fontSize: 14,
                          fontWeight: pw.FontWeight.bold,
                          color: PdfColors.green600,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildModernNotes(Invoice invoice) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.yellow50,
        borderRadius: pw.BorderRadius.circular(12),
        border: pw.Border.all(color: PdfColors.yellow200),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Notes',
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.yellow800,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            invoice.notes,
            style: const pw.TextStyle(fontSize: 12),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildModernTermsAndConditions(Invoice invoice) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey50,
        borderRadius: pw.BorderRadius.circular(12),
        border: pw.Border.all(color: PdfColors.grey200),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Terms & Conditions',
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.grey800,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            invoice.termsAndConditions,
            style: const pw.TextStyle(fontSize: 11),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildModernInvoiceFooter(Invoice invoice) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        gradient: const pw.LinearGradient(
          colors: [PdfColors.grey100, PdfColors.grey200],
        ),
        borderRadius: pw.BorderRadius.circular(12),
      ),
      child: pw.Column(
        children: [
          pw.Text(
            'Thank you for your business!',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.grey800,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            'Generated on ${DateFormat('MMM dd, yyyy HH:mm').format(DateTime.now())}',
            style: const pw.TextStyle(
              fontSize: 10,
              color: PdfColors.grey600,
            ),
          ),
        ],
      ),
    );
  }

  // =================== INVOICE QR CODE GENERATION ===================

  static Future<pw.Widget> _generateInvoiceQrCodeWidget(Invoice invoice, UpiAccount upiAccount) async {
    // Generate UPI payment string for invoice
    final paymentString = PaymentService.instance.generateUpiPaymentString(
      upiId: upiAccount.upiId,
      merchantName: upiAccount.merchantName,
      amount: invoice.totalAmount,
      transactionNote: 'Payment for ${invoice.invoiceNumber}',
      merchantCode: upiAccount.merchantCode,
    );

    return pw.BarcodeWidget(
      barcode: pw.Barcode.qrCode(),
      data: paymentString,
      width: 100,
      height: 100,
    );
  }

  // =================== MODERN HELPER METHODS ===================

  static pw.Widget _buildModernBankDetailsSection(BankAccount bankAccount) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.blue200),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Bank Transfer',
            style: pw.TextStyle(
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue800,
            ),
          ),
          pw.SizedBox(height: 8),
          _buildDetailRow('Account Name', bankAccount.accountHolderName),
          _buildDetailRow('Account Number', bankAccount.accountNumber),
          _buildDetailRow('IFSC Code', bankAccount.ifscCode),
          _buildDetailRow('Bank Name', bankAccount.bankName),
          if (bankAccount.branchName.isNotEmpty)
            _buildDetailRow('Branch', bankAccount.branchName),
        ],
      ),
    );
  }

  static pw.Widget _buildModernUpiDetailsSection(UpiAccount upiAccount) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: PdfColors.green50,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.green200),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'UPI Payment',
            style: pw.TextStyle(
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.green800,
            ),
          ),
          pw.SizedBox(height: 8),
          _buildDetailRow('UPI ID', upiAccount.upiId),
          _buildDetailRow('Name', upiAccount.merchantName),
          if (upiAccount.merchantCode?.isNotEmpty == true)
            _buildDetailRow('Merchant Code', upiAccount.merchantCode!),
        ],
      ),
    );
  }

  static pw.Widget _buildModernQrCodeSection(pw.Widget qrCodeWidget, double totalAmount) {
    final currencyFormat = NumberFormat.currency(symbol: '₹');

    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: PdfColors.purple50,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.purple200),
      ),
      child: pw.Column(
        children: [
          pw.Text(
            'Scan to Pay',
            style: pw.TextStyle(
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.purple800,
            ),
          ),
          pw.SizedBox(height: 8),
          qrCodeWidget,
          pw.SizedBox(height: 8),
          pw.Text(
            currencyFormat.format(totalAmount),
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.purple800,
            ),
          ),
        ],
      ),
    );
  }

  // =================== CLASSIC INVOICE TEMPLATE ===================

  static Future<File> _generateClassicInvoicePdf({
    required Invoice invoice,
    BankAccount? bankAccount,
    UpiAccount? upiAccount,
    bool showBankDetails = true,
    bool showUpiDetails = true,
    bool showQrCode = true,
    String? paymentInstructions,
  }) async {
    final pdf = pw.Document();
    final dateFormat = DateFormat('MMM dd, yyyy');
    final currencyFormat = NumberFormat.currency(symbol: '₹');

    // Generate QR code if needed
    pw.Widget? qrCodeWidget;
    if (showQrCode && upiAccount != null) {
      qrCodeWidget = await _generateInvoiceQrCodeWidget(invoice, upiAccount);
    }

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(40),
        build: (context) {
          return [
            // Classic Header
            _buildModernInvoiceHeader(invoice, dateFormat),
            pw.SizedBox(height: 30),

            // Client Details
            _buildModernClientDetails(invoice),
            pw.SizedBox(height: 30),

            // Items Table
            _buildModernInvoiceItemsTable(invoice, currencyFormat),
            pw.SizedBox(height: 20),

            // Total Summary
            _buildModernInvoiceTotalSummary(invoice, currencyFormat),
            pw.SizedBox(height: 30),

            // Payment Information
            if (showBankDetails || showUpiDetails || showQrCode)
              _buildPaymentSection(
                bankAccount: bankAccount,
                upiAccount: upiAccount,
                showBankDetails: showBankDetails,
                showUpiDetails: showUpiDetails,
                qrCodeWidget: qrCodeWidget,
                paymentInstructions: paymentInstructions,
                totalAmount: invoice.totalAmount,
              ),

            // Payment History if any
            if (invoice.payments.isNotEmpty) ...[
              pw.SizedBox(height: 30),
              _buildPaymentHistory(invoice, currencyFormat),
            ],

            // Notes and Terms
            if (invoice.notes.isNotEmpty) ...[
              pw.SizedBox(height: 20),
              _buildInvoiceNotes(invoice),
            ],

            if (invoice.termsAndConditions.isNotEmpty) ...[
              pw.SizedBox(height: 20),
              _buildInvoiceTermsAndConditions(invoice),
            ],

            // Footer
            pw.SizedBox(height: 30),
            _buildFooter(),
          ];
        },
      ),
    );

    // Save PDF
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/invoice_${invoice.invoiceNumber}_classic.pdf');
    await file.writeAsBytes(await pdf.save());

    return file;
  }

  static pw.Widget _buildHeader(Quotation quotation, DateFormat dateFormat) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.blue, width: 2),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      padding: const pw.EdgeInsets.all(20),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'QUOTATION',
                    style: pw.TextStyle(
                      fontSize: 28,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.blue,
                    ),
                  ),
                  pw.SizedBox(height: 8),
                  pw.Text(
                    'Quote #${quotation.quotationNumber}',
                    style: pw.TextStyle(
                      fontSize: 16,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                ],
              ),
              pw.Container(
                padding: const pw.EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: pw.BoxDecoration(
                  color: _getStatusColor(quotation.status),
                  borderRadius: pw.BorderRadius.circular(20),
                ),
                child: pw.Text(
                  quotation.statusText.toUpperCase(),
                  style: pw.TextStyle(
                    color: PdfColors.white,
                    fontWeight: pw.FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 16),
          pw.Divider(thickness: 1),
          pw.SizedBox(height: 16),
          pw.Row(
            children: [
              pw.Expanded(
                child: _buildInfoItem(
                  'Quote Date:',
                  dateFormat.format(quotation.createdDate),
                ),
              ),
              pw.Expanded(
                child: _buildInfoItem(
                  'Valid Until:',
                  dateFormat.format(quotation.validUntil),
                ),
              ),
              pw.Expanded(
                child: _buildInfoItem(
                  'Created By:',
                  quotation.createdBy,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildClientDetails(Quotation quotation) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      padding: const pw.EdgeInsets.all(16),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Bill To:',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 12),
          pw.Text(
            quotation.client.name,
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          if (quotation.client.company.isNotEmpty) ...[
            pw.SizedBox(height: 4),
            pw.Text(quotation.client.company),
          ],
          pw.SizedBox(height: 4),
          pw.Text(quotation.client.email),
          pw.SizedBox(height: 4),
          pw.Text(quotation.client.phone),
          pw.SizedBox(height: 4),
          pw.Text(quotation.client.address),
        ],
      ),
    );
  }

  static pw.Widget _buildItemsTable(Quotation quotation, NumberFormat currencyFormat) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        children: [
          pw.Container(
            padding: const pw.EdgeInsets.all(16),
            decoration: pw.BoxDecoration(
              color: PdfColors.grey100,
              borderRadius: const pw.BorderRadius.only(
                topLeft: pw.Radius.circular(8),
                topRight: pw.Radius.circular(8),
              ),
            ),
            child: pw.Text(
              'Items',
              style: pw.TextStyle(
                fontSize: 16,
                fontWeight: pw.FontWeight.bold,
              ),
            ),
          ),
          pw.Table(
            border: pw.TableBorder.all(color: PdfColors.grey300),
            children: [
              // Header
              pw.TableRow(
                decoration: const pw.BoxDecoration(color: PdfColors.grey50),
                children: [
                  _buildTableCell('Description', isHeader: true),
                  _buildTableCell('Qty', isHeader: true),
                  _buildTableCell('Unit', isHeader: true),
                  _buildTableCell('Unit Price', isHeader: true),
                  _buildTableCell('Discount', isHeader: true, alignment: pw.Alignment.centerRight),
                  _buildTableCell('Total', isHeader: true, alignment: pw.Alignment.centerRight),
                ],
              ),
              // Items
              ...quotation.items.map((item) {
                return pw.TableRow(
                  children: [
                    _buildTableCell(item.description),
                    _buildTableCell(item.quantity.toString()),
                    _buildTableCell(item.unit),
                    _buildTableCell(currencyFormat.format(item.unitPrice), alignment: pw.Alignment.centerRight),
                    _buildTableCell(currencyFormat.format(item.discount), alignment: pw.Alignment.centerRight),
                    _buildTableCell(
                      currencyFormat.format(item.totalPrice),
                      alignment: pw.Alignment.centerRight,
                      isBold: true,
                    ),
                  ],
                );
              }),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildTotalSummary(Quotation quotation, NumberFormat currencyFormat) {
    return pw.Container(
      width: 250,
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      padding: const pw.EdgeInsets.all(16),
      child: pw.Column(
        children: [
          _buildSummaryRow('Subtotal:', currencyFormat.format(quotation.subtotal)),
          if (quotation.totalDiscount > 0)
            _buildSummaryRow(
              'Discount:',
              '- ${currencyFormat.format(quotation.totalDiscount)}',
              valueColor: PdfColors.red,
            ),
          if (quotation.taxRate > 0)
            _buildSummaryRow(
              'Tax (${quotation.taxRate}%):',
              currencyFormat.format(quotation.taxAmount),
            ),
          if (quotation.shippingCost > 0)
            _buildSummaryRow(
              'Shipping:',
              currencyFormat.format(quotation.shippingCost),
            ),
          pw.Divider(thickness: 2),
          _buildSummaryRow(
            'Total:',
            currencyFormat.format(quotation.totalAmount),
            isBold: true,
            fontSize: 16,
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildPaymentSection({
    BankAccount? bankAccount,
    UpiAccount? upiAccount,
    bool showBankDetails = true,
    bool showUpiDetails = true,
    pw.Widget? qrCodeWidget,
    String? paymentInstructions,
    required double totalAmount,
  }) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      padding: const pw.EdgeInsets.all(16),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Payment Information',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 16),
          
          // Payment Instructions
          if (paymentInstructions != null && paymentInstructions.isNotEmpty) ...[
            pw.Container(
              padding: const pw.EdgeInsets.all(12),
              decoration: pw.BoxDecoration(
                color: PdfColors.blue50,
                borderRadius: pw.BorderRadius.circular(6),
                border: pw.Border.all(color: PdfColors.blue200),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'Payment Instructions:',
                    style: pw.TextStyle(
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.blue700,
                    ),
                  ),
                  pw.SizedBox(height: 4),
                  pw.Text(paymentInstructions),
                ],
              ),
            ),
            pw.SizedBox(height: 16),
          ],
          
          pw.Row(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Bank Details
              if (showBankDetails && bankAccount != null) ...[
                pw.Expanded(
                  child: _buildBankDetailsSection(bankAccount),
                ),
                pw.SizedBox(width: 16),
              ],
              
              // UPI Details
              if (showUpiDetails && upiAccount != null) ...[
                pw.Expanded(
                  child: _buildUpiDetailsSection(upiAccount),
                ),
                if (qrCodeWidget != null) pw.SizedBox(width: 16),
              ],
              
              // QR Code
              if (qrCodeWidget != null) ...[
                _buildQrCodeSection(qrCodeWidget, totalAmount),
              ],
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildBankDetailsSection(BankAccount bankAccount) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: PdfColors.green50,
        borderRadius: pw.BorderRadius.circular(6),
        border: pw.Border.all(color: PdfColors.green200),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Bank Transfer',
            style: pw.TextStyle(
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.green700,
            ),
          ),
          pw.SizedBox(height: 8),
          _buildDetailRow('Bank Name:', bankAccount.bankName),
          _buildDetailRow('Account Holder:', bankAccount.accountHolderName),
          _buildDetailRow('Account Number:', bankAccount.accountNumber),
          _buildDetailRow('IFSC Code:', bankAccount.ifscCode),
          _buildDetailRow('Branch:', bankAccount.branchName),
          if (bankAccount.swiftCode != null)
            _buildDetailRow('SWIFT Code:', bankAccount.swiftCode!),
        ],
      ),
    );
  }

  static pw.Widget _buildUpiDetailsSection(UpiAccount upiAccount) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: PdfColors.purple50,
        borderRadius: pw.BorderRadius.circular(6),
        border: pw.Border.all(color: PdfColors.purple200),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'UPI Payment',
            style: pw.TextStyle(
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.purple700,
            ),
          ),
          pw.SizedBox(height: 8),
          _buildDetailRow('UPI ID:', upiAccount.upiId),
          _buildDetailRow('Merchant:', upiAccount.merchantName),
          if (upiAccount.merchantCode != null)
            _buildDetailRow('Merchant Code:', upiAccount.merchantCode!),
        ],
      ),
    );
  }

  static pw.Widget _buildQrCodeSection(pw.Widget qrCodeWidget, double totalAmount) {
    return pw.Container(
      width: 120,
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey50,
        borderRadius: pw.BorderRadius.circular(6),
        border: pw.Border.all(color: PdfColors.grey300),
      ),
      child: pw.Column(
        children: [
          pw.Text(
            'Scan to Pay',
            style: pw.TextStyle(
              fontWeight: pw.FontWeight.bold,
              fontSize: 10,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Container(
            width: 80,
            height: 80,
            child: qrCodeWidget,
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            '₹${NumberFormat('#,##0.00').format(totalAmount)}',
            style: pw.TextStyle(
              fontSize: 10,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildNotes(Quotation quotation) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      padding: const pw.EdgeInsets.all(16),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Notes:',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Text(quotation.notes),
        ],
      ),
    );
  }

  static pw.Widget _buildFooter() {
    return pw.Container(
      alignment: pw.Alignment.center,
      child: pw.Column(
        children: [
          pw.Divider(),
          pw.SizedBox(height: 8),
          pw.Text(
            'Thank you for your business!',
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue,
            ),
          ),
          pw.SizedBox(height: 4),
          pw.Text(
            'Generated on ${DateFormat('MMM dd, yyyy HH:mm').format(DateTime.now())}',
            style: const pw.TextStyle(
              fontSize: 10,
              color: PdfColors.grey,
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildInfoItem(String label, String value) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          label,
          style: pw.TextStyle(
            fontSize: 10,
            color: PdfColors.grey600,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 2),
        pw.Text(
          value,
          style: const pw.TextStyle(fontSize: 12),
        ),
      ],
    );
  }

  static pw.Widget _buildTableCell(
    String text, {
    bool isHeader = false,
    bool isBold = false,
    pw.Alignment alignment = pw.Alignment.centerLeft,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      alignment: alignment,
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 12 : 10,
          fontWeight: (isHeader || isBold) ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
      ),
    );
  }

  static pw.Widget _buildSummaryRow(
    String label,
    String value, {
    bool isBold = false,
    double fontSize = 12,
    PdfColor? valueColor,
  }) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(
              fontSize: fontSize,
              fontWeight: isBold ? pw.FontWeight.bold : pw.FontWeight.normal,
            ),
          ),
          pw.Text(
            value,
            style: pw.TextStyle(
              fontSize: fontSize,
              fontWeight: isBold ? pw.FontWeight.bold : pw.FontWeight.normal,
              color: valueColor ?? PdfColors.black,
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildDetailRow(String label, String value) {
    return pw.Padding(
      padding: const pw.EdgeInsets.only(bottom: 4),
      child: pw.Row(
        children: [
          pw.SizedBox(
            width: 80,
            child: pw.Text(
              label,
              style: const pw.TextStyle(
                fontSize: 9,
                color: PdfColors.grey600,
              ),
            ),
          ),
          pw.Expanded(
            child: pw.Text(
              value,
              style: pw.TextStyle(
                fontSize: 9,
                fontWeight: pw.FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Future<pw.Widget> _generateQrCodeWidget(
    Quotation quotation,
    UpiAccount upiAccount,
  ) async {
    // Generate UPI QR Data
    final upiData = StringBuffer();
    upiData.write('upi://pay?');
    upiData.write('pa=${upiAccount.upiId}');
    upiData.write('&pn=${Uri.encodeComponent(upiAccount.merchantName)}');
    upiData.write('&am=${quotation.totalAmount.toStringAsFixed(2)}');
    upiData.write('&cu=INR');
    upiData.write('&tn=${Uri.encodeComponent('Payment for Quote ${quotation.quotationNumber}')}');

    return pw.BarcodeWidget(
      barcode: pw.Barcode.qrCode(),
      data: upiData.toString(),
      width: 80,
      height: 80,
    );
  }

  static PdfColor _getStatusColor(QuotationStatus status) {
    switch (status) {
      case QuotationStatus.draft:
        return PdfColors.grey;
      case QuotationStatus.sent:
        return PdfColors.blue;
      case QuotationStatus.accepted:
        return PdfColors.green;
      case QuotationStatus.rejected:
        return PdfColors.red;
      case QuotationStatus.expired:
        return PdfColors.orange;
    }
  }

  // =================== MISSING INVOICE PDF METHODS ===================

  static Future<Uint8List> _generateMinimalInvoicePdf({
    required Invoice invoice,
    BankAccount? bankAccount,
    UpiAccount? upiAccount,
    bool showBankDetails = true,
    bool showUpiDetails = true,
    bool showQrCode = true,
    String? paymentInstructions,
  }) async {
    // For now, use the modern template as fallback
    return await _generateModernInvoicePdf(
      invoice: invoice,
      bankAccount: bankAccount,
      upiAccount: upiAccount,
      showBankDetails: showBankDetails,
      showUpiDetails: showUpiDetails,
      showQrCode: showQrCode,
      paymentInstructions: paymentInstructions,
    );
  }

  static Future<Uint8List> _generateProfessionalInvoicePdf({
    required Invoice invoice,
    BankAccount? bankAccount,
    UpiAccount? upiAccount,
    bool showBankDetails = true,
    bool showUpiDetails = true,
    bool showQrCode = true,
    String? paymentInstructions,
  }) async {
    // For now, use the modern template as fallback
    return await _generateModernInvoicePdf(
      invoice: invoice,
      bankAccount: bankAccount,
      upiAccount: upiAccount,
      showBankDetails: showBankDetails,
      showUpiDetails: showUpiDetails,
      showQrCode: showQrCode,
      paymentInstructions: paymentInstructions,
    );
  }

  // =================== INVOICE-SPECIFIC HELPER METHODS ===================

  static pw.Widget _buildInvoiceNotes(Invoice invoice) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'NOTES',
            style: pw.TextStyle(
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.grey700,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            invoice.notes,
            style: const pw.TextStyle(fontSize: 10),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildInvoiceTermsAndConditions(Invoice invoice) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'TERMS & CONDITIONS',
            style: pw.TextStyle(
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.grey700,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            invoice.termsAndConditions,
            style: const pw.TextStyle(fontSize: 10),
          ),
        ],
      ),
    );
  }
}