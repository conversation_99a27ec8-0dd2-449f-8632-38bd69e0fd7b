import 'package:flutter/material.dart';

class EmptyStateWidget extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final String? actionText;
  final VoidCallback? onActionPressed;
  final Widget? customAction;
  final Color? iconColor;
  final double? iconSize;

  const EmptyStateWidget({
    super.key,
    required this.icon,
    required this.title,
    required this.subtitle,
    this.actionText,
    this.onActionPressed,
    this.customAction,
    this.iconColor,
    this.iconSize,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: iconSize ?? 80,
              color: iconColor ?? theme.colorScheme.onSurface.withOpacity(0.3),
            ),
            const SizedBox(height: 24),
            Text(
              title,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface.withOpacity(0.8),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              subtitle,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            if (customAction != null)
              customAction!
            else if (actionText != null && onActionPressed != null)
              ElevatedButton.icon(
                onPressed: onActionPressed,
                icon: const Icon(Icons.add),
                label: Text(actionText!),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class EmptySearchWidget extends StatelessWidget {
  final String searchQuery;
  final VoidCallback? onClearSearch;

  const EmptySearchWidget({
    super.key,
    required this.searchQuery,
    this.onClearSearch,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.search_off,
      title: 'No Results Found',
      subtitle: 'No items match your search for "$searchQuery".\nTry adjusting your search terms.',
      actionText: 'Clear Search',
      onActionPressed: onClearSearch,
    );
  }
}

class EmptyFilterWidget extends StatelessWidget {
  final VoidCallback? onClearFilters;

  const EmptyFilterWidget({
    super.key,
    this.onClearFilters,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.filter_list_off,
      title: 'No Items Match Filters',
      subtitle: 'Try adjusting your filter criteria to see more results.',
      actionText: 'Clear Filters',
      onActionPressed: onClearFilters,
    );
  }
}

class NetworkErrorWidget extends StatelessWidget {
  final String? message;
  final VoidCallback? onRetry;

  const NetworkErrorWidget({
    super.key,
    this.message,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.wifi_off,
      iconColor: Colors.red[300],
      title: 'Connection Error',
      subtitle: message ?? 'Please check your internet connection and try again.',
      actionText: 'Retry',
      onActionPressed: onRetry,
    );
  }
}

class ErrorStateWidget extends StatelessWidget {
  final String? title;
  final String? message;
  final VoidCallback? onRetry;
  final IconData? icon;

  const ErrorStateWidget({
    super.key,
    this.title,
    this.message,
    this.onRetry,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: icon ?? Icons.error_outline,
      iconColor: Colors.red[300],
      title: title ?? 'Something Went Wrong',
      subtitle: message ?? 'An unexpected error occurred. Please try again.',
      actionText: onRetry != null ? 'Try Again' : null,
      onActionPressed: onRetry,
    );
  }
}

class MaintenanceWidget extends StatelessWidget {
  final String? message;

  const MaintenanceWidget({
    super.key,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.build,
      iconColor: Colors.orange[300],
      title: 'Under Maintenance',
      subtitle: message ?? 'We\'re currently performing maintenance. Please check back later.',
    );
  }
}

class PermissionDeniedWidget extends StatelessWidget {
  final String? message;
  final VoidCallback? onRequestPermission;

  const PermissionDeniedWidget({
    super.key,
    this.message,
    this.onRequestPermission,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.lock,
      iconColor: Colors.orange[300],
      title: 'Permission Required',
      subtitle: message ?? 'This feature requires additional permissions to work properly.',
      actionText: onRequestPermission != null ? 'Grant Permission' : null,
      onActionPressed: onRequestPermission,
    );
  }
}

class ComingSoonWidget extends StatelessWidget {
  final String? feature;
  final String? message;

  const ComingSoonWidget({
    super.key,
    this.feature,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.upcoming,
      iconColor: Colors.blue[300],
      title: feature != null ? '$feature Coming Soon' : 'Coming Soon',
      subtitle: message ?? 'This feature is currently under development and will be available soon.',
    );
  }
}

class OfflineWidget extends StatelessWidget {
  final VoidCallback? onRetry;

  const OfflineWidget({
    super.key,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.cloud_off,
      iconColor: Colors.grey[400],
      title: 'You\'re Offline',
      subtitle: 'Some features may not be available while offline. Connect to the internet for the full experience.',
      actionText: onRetry != null ? 'Try Again' : null,
      onActionPressed: onRetry,
    );
  }
}
