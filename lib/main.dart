import 'package:an_dashboard/firebase_options.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:responsive_framework/responsive_framework.dart';

import 'blocs/dashboard/dashboard_bloc.dart';
import 'blocs/quotes/quotes_bloc.dart';
import 'blocs/quotations/quotations_bloc.dart';
import 'blocs/clients/clients_bloc.dart';
import 'blocs/invoice/invoice_bloc.dart';
import 'repositories/dashboard_repository.dart';
import 'repositories/quotes_repository.dart';
import 'repositories/firebase_quotation_repository.dart';
import 'repositories/invoice_repository.dart';
import 'services/api_service.dart';
import 'services/dashboard_data_service.dart';
import 'ui/screens/auth_wrapper.dart';
import 'ui/theme/app_theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Initialize dashboard data
  final dashboardDataService = DashboardDataService();
  await dashboardDataService.initializeDashboardData();
  
  // Start real-time updates simulation
  dashboardDataService.simulateRealtimeUpdates();

  runApp(const ANDashboardApp());
}

class ANDashboardApp extends StatelessWidget {
  const ANDashboardApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiRepositoryProvider(
      providers: [
        RepositoryProvider<ApiService>(
          create: (context) => ApiService(),
        ),
        RepositoryProvider<DashboardRepository>(
          create: (context) => DashboardRepository(
            apiService: context.read<ApiService>(),
          ),
        ),
        RepositoryProvider<QuotesRepository>(
          create: (context) => QuotesRepository(
            apiService: context.read<ApiService>(),
          ),
        ),
        RepositoryProvider<FirebaseQuotationRepository>(
          create: (context) => FirebaseQuotationRepository(),
        ),
      ],
      child: MultiBlocProvider(
        providers: [
          BlocProvider<DashboardBloc>(
            create: (context) => DashboardBloc(
              repository: context.read<DashboardRepository>(),
            ),
          ),
          BlocProvider<QuotesBloc>(
            create: (context) => QuotesBloc(
              repository: context.read<QuotesRepository>(),
            ),
          ),
          BlocProvider<QuotationsBloc>(
            create: (context) => QuotationsBloc(
              repository: context.read<FirebaseQuotationRepository>(),
            ),
          ),
          BlocProvider<ClientsBloc>(
            create: (context) => ClientsBloc(
              repository: context.read<FirebaseQuotationRepository>(),
            ),
          ),
        ],
        child: MaterialApp(
          title: 'AN Enterprises Dashboard',
          debugShowCheckedModeBanner: false,
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeMode.system,
          builder: (context, child) => ResponsiveBreakpoints.builder(
            child: child!,
            breakpoints: [
              const Breakpoint(start: 0, end: 450, name: MOBILE),
              const Breakpoint(start: 451, end: 800, name: TABLET),
              const Breakpoint(start: 801, end: 1920, name: DESKTOP),
              const Breakpoint(start: 1921, end: double.infinity, name: '4K'),
            ],
          ),
          home: const AuthWrapper(),
        ),
      ),
    );
  }
}
