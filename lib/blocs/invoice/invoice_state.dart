import 'package:equatable/equatable.dart';
import '../../models/invoice_models.dart';

abstract class InvoiceState extends Equatable {
  const InvoiceState();

  @override
  List<Object?> get props => [];
}

// =================== INITIAL STATE ===================

class InvoiceInitial extends InvoiceState {
  const InvoiceInitial();
}

// =================== LOADING STATES ===================

class InvoiceLoading extends InvoiceState {
  const InvoiceLoading();
}

class InvoiceOperationLoading extends InvoiceState {
  final String operation;

  const InvoiceOperationLoading(this.operation);

  @override
  List<Object> get props => [operation];
}

// =================== SUCCESS STATES ===================

class InvoicesLoaded extends InvoiceState {
  final List<Invoice> invoices;
  final List<Invoice> filteredInvoices;
  final InvoiceStatus? statusFilter;
  final PaymentStatus? paymentStatusFilter;
  final String? searchTerm;
  final DateTime? startDateFilter;
  final DateTime? endDateFilter;

  const InvoicesLoaded({
    required this.invoices,
    required this.filteredInvoices,
    this.statusFilter,
    this.paymentStatusFilter,
    this.searchTerm,
    this.startDateFilter,
    this.endDateFilter,
  });

  @override
  List<Object?> get props => [
        invoices,
        filteredInvoices,
        statusFilter,
        paymentStatusFilter,
        searchTerm,
        startDateFilter,
        endDateFilter,
      ];

  InvoicesLoaded copyWith({
    List<Invoice>? invoices,
    List<Invoice>? filteredInvoices,
    InvoiceStatus? statusFilter,
    PaymentStatus? paymentStatusFilter,
    String? searchTerm,
    DateTime? startDateFilter,
    DateTime? endDateFilter,
    bool clearStatusFilter = false,
    bool clearPaymentStatusFilter = false,
    bool clearSearchTerm = false,
    bool clearStartDateFilter = false,
    bool clearEndDateFilter = false,
  }) {
    return InvoicesLoaded(
      invoices: invoices ?? this.invoices,
      filteredInvoices: filteredInvoices ?? this.filteredInvoices,
      statusFilter: clearStatusFilter ? null : (statusFilter ?? this.statusFilter),
      paymentStatusFilter: clearPaymentStatusFilter ? null : (paymentStatusFilter ?? this.paymentStatusFilter),
      searchTerm: clearSearchTerm ? null : (searchTerm ?? this.searchTerm),
      startDateFilter: clearStartDateFilter ? null : (startDateFilter ?? this.startDateFilter),
      endDateFilter: clearEndDateFilter ? null : (endDateFilter ?? this.endDateFilter),
    );
  }
}

class InvoiceLoaded extends InvoiceState {
  final Invoice invoice;

  const InvoiceLoaded(this.invoice);

  @override
  List<Object> get props => [invoice];
}

class InvoiceStatisticsLoaded extends InvoiceState {
  final Map<String, dynamic> statistics;

  const InvoiceStatisticsLoaded(this.statistics);

  @override
  List<Object> get props => [statistics];
}

class OverdueInvoicesLoaded extends InvoiceState {
  final List<Invoice> overdueInvoices;

  const OverdueInvoicesLoaded(this.overdueInvoices);

  @override
  List<Object> get props => [overdueInvoices];
}

class InvoiceCreated extends InvoiceState {
  final Invoice invoice;
  final String message;

  const InvoiceCreated({
    required this.invoice,
    this.message = 'Invoice created successfully',
  });

  @override
  List<Object> get props => [invoice, message];
}

class InvoiceUpdated extends InvoiceState {
  final Invoice invoice;
  final String message;

  const InvoiceUpdated({
    required this.invoice,
    this.message = 'Invoice updated successfully',
  });

  @override
  List<Object> get props => [invoice, message];
}

class InvoiceDeleted extends InvoiceState {
  final String invoiceId;
  final String message;

  const InvoiceDeleted({
    required this.invoiceId,
    this.message = 'Invoice deleted successfully',
  });

  @override
  List<Object> get props => [invoiceId, message];
}

class InvoiceStatusUpdated extends InvoiceState {
  final String invoiceId;
  final InvoiceStatus status;
  final String message;

  const InvoiceStatusUpdated({
    required this.invoiceId,
    required this.status,
    this.message = 'Invoice status updated successfully',
  });

  @override
  List<Object> get props => [invoiceId, status, message];
}

class PaymentStatusUpdated extends InvoiceState {
  final String invoiceId;
  final PaymentStatus paymentStatus;
  final String message;

  const PaymentStatusUpdated({
    required this.invoiceId,
    required this.paymentStatus,
    this.message = 'Payment status updated successfully',
  });

  @override
  List<Object> get props => [invoiceId, paymentStatus, message];
}

class PaymentAdded extends InvoiceState {
  final String invoiceId;
  final PaymentRecord payment;
  final String message;

  const PaymentAdded({
    required this.invoiceId,
    required this.payment,
    this.message = 'Payment added successfully',
  });

  @override
  List<Object> get props => [invoiceId, payment, message];
}

class InvoiceNumberGenerated extends InvoiceState {
  final String invoiceNumber;

  const InvoiceNumberGenerated(this.invoiceNumber);

  @override
  List<Object> get props => [invoiceNumber];
}

class BulkOperationCompleted extends InvoiceState {
  final String operation;
  final int affectedCount;
  final String message;

  const BulkOperationCompleted({
    required this.operation,
    required this.affectedCount,
    required this.message,
  });

  @override
  List<Object> get props => [operation, affectedCount, message];
}

// =================== ERROR STATES ===================

class InvoiceError extends InvoiceState {
  final String message;
  final String? errorCode;

  const InvoiceError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

class InvoiceOperationError extends InvoiceState {
  final String operation;
  final String message;
  final String? errorCode;

  const InvoiceOperationError({
    required this.operation,
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [operation, message, errorCode];
}

// =================== UTILITY STATES ===================

class InvoiceEmpty extends InvoiceState {
  final String message;

  const InvoiceEmpty({
    this.message = 'No invoices found',
  });

  @override
  List<Object> get props => [message];
}
