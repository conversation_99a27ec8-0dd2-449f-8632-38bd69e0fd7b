import 'package:equatable/equatable.dart';
import '../../models/invoice_models.dart';
import '../../models/quotation_models.dart';

abstract class InvoiceEvent extends Equatable {
  const InvoiceEvent();

  @override
  List<Object?> get props => [];
}

// =================== LOADING EVENTS ===================

class LoadInvoices extends InvoiceEvent {
  final InvoiceStatus? status;
  final PaymentStatus? paymentStatus;
  final String? searchTerm;
  final DateTime? startDate;
  final DateTime? endDate;

  const LoadInvoices({
    this.status,
    this.paymentStatus,
    this.searchTerm,
    this.startDate,
    this.endDate,
  });

  @override
  List<Object?> get props => [status, paymentStatus, searchTerm, startDate, endDate];
}

class LoadInvoiceById extends InvoiceEvent {
  final String invoiceId;

  const LoadInvoiceById(this.invoiceId);

  @override
  List<Object> get props => [invoiceId];
}

class LoadInvoiceStatistics extends InvoiceEvent {
  const LoadInvoiceStatistics();
}

class LoadOverdueInvoices extends InvoiceEvent {
  const LoadOverdueInvoices();
}

// =================== CRUD EVENTS ===================

class CreateInvoice extends InvoiceEvent {
  final Invoice invoice;

  const CreateInvoice(this.invoice);

  @override
  List<Object> get props => [invoice];
}

class CreateInvoiceFromQuotation extends InvoiceEvent {
  final Quotation quotation;
  final DateTime? issueDate;
  final DateTime? dueDate;
  final InvoiceTemplate template;

  const CreateInvoiceFromQuotation({
    required this.quotation,
    this.issueDate,
    this.dueDate,
    this.template = InvoiceTemplate.modern,
  });

  @override
  List<Object?> get props => [quotation, issueDate, dueDate, template];
}

class UpdateInvoice extends InvoiceEvent {
  final String invoiceId;
  final Invoice invoice;

  const UpdateInvoice({
    required this.invoiceId,
    required this.invoice,
  });

  @override
  List<Object> get props => [invoiceId, invoice];
}

class DeleteInvoice extends InvoiceEvent {
  final String invoiceId;

  const DeleteInvoice(this.invoiceId);

  @override
  List<Object> get props => [invoiceId];
}

// =================== STATUS EVENTS ===================

class UpdateInvoiceStatus extends InvoiceEvent {
  final String invoiceId;
  final InvoiceStatus status;

  const UpdateInvoiceStatus({
    required this.invoiceId,
    required this.status,
  });

  @override
  List<Object> get props => [invoiceId, status];
}

class UpdatePaymentStatus extends InvoiceEvent {
  final String invoiceId;
  final PaymentStatus paymentStatus;

  const UpdatePaymentStatus({
    required this.invoiceId,
    required this.paymentStatus,
  });

  @override
  List<Object> get props => [invoiceId, paymentStatus];
}

class MarkInvoiceAsSent extends InvoiceEvent {
  final String invoiceId;

  const MarkInvoiceAsSent(this.invoiceId);

  @override
  List<Object> get props => [invoiceId];
}

class MarkInvoiceAsPaid extends InvoiceEvent {
  final String invoiceId;
  final PaymentRecord? paymentRecord;

  const MarkInvoiceAsPaid({
    required this.invoiceId,
    this.paymentRecord,
  });

  @override
  List<Object?> get props => [invoiceId, paymentRecord];
}

class CancelInvoice extends InvoiceEvent {
  final String invoiceId;

  const CancelInvoice(this.invoiceId);

  @override
  List<Object> get props => [invoiceId];
}

// =================== PAYMENT EVENTS ===================

class AddPayment extends InvoiceEvent {
  final String invoiceId;
  final PaymentRecord payment;

  const AddPayment({
    required this.invoiceId,
    required this.payment,
  });

  @override
  List<Object> get props => [invoiceId, payment];
}

// =================== SEARCH & FILTER EVENTS ===================

class SearchInvoices extends InvoiceEvent {
  final String searchTerm;

  const SearchInvoices(this.searchTerm);

  @override
  List<Object> get props => [searchTerm];
}

class FilterInvoicesByStatus extends InvoiceEvent {
  final InvoiceStatus? status;

  const FilterInvoicesByStatus(this.status);

  @override
  List<Object?> get props => [status];
}

class FilterInvoicesByPaymentStatus extends InvoiceEvent {
  final PaymentStatus? paymentStatus;

  const FilterInvoicesByPaymentStatus(this.paymentStatus);

  @override
  List<Object?> get props => [paymentStatus];
}

class FilterInvoicesByDateRange extends InvoiceEvent {
  final DateTime? startDate;
  final DateTime? endDate;

  const FilterInvoicesByDateRange({
    this.startDate,
    this.endDate,
  });

  @override
  List<Object?> get props => [startDate, endDate];
}

class ClearInvoiceFilters extends InvoiceEvent {
  const ClearInvoiceFilters();
}

// =================== BULK OPERATIONS ===================

class BulkUpdateInvoiceStatus extends InvoiceEvent {
  final List<String> invoiceIds;
  final InvoiceStatus status;

  const BulkUpdateInvoiceStatus({
    required this.invoiceIds,
    required this.status,
  });

  @override
  List<Object> get props => [invoiceIds, status];
}

class BulkDeleteInvoices extends InvoiceEvent {
  final List<String> invoiceIds;

  const BulkDeleteInvoices(this.invoiceIds);

  @override
  List<Object> get props => [invoiceIds];
}

// =================== UTILITY EVENTS ===================

class RefreshInvoices extends InvoiceEvent {
  const RefreshInvoices();
}

class ResetInvoiceState extends InvoiceEvent {
  const ResetInvoiceState();
}

class GenerateInvoiceNumber extends InvoiceEvent {
  const GenerateInvoiceNumber();
}
