import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/invoice_models.dart';
import '../models/quotation_models.dart';
import 'invoice_service.dart';

class FirebaseInvoiceService {
  static FirebaseInvoiceService? _instance;
  static FirebaseInvoiceService get instance => _instance ??= FirebaseInvoiceService._();
  FirebaseInvoiceService._();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final InvoiceService _localService = InvoiceService.instance;

  String get _userId => _auth.currentUser?.uid ?? 'anonymous';
  CollectionReference get _invoicesCollection => 
      _firestore.collection('users').doc(_userId).collection('invoices');

  // Create invoice
  Future<String> createInvoice(Invoice invoice) async {
    try {
      final docRef = await _invoicesCollection.add(invoice.toJson());
      
      // Also save locally
      final invoiceWithId = invoice.copyWith(id: docRef.id);
      await _localService.saveInvoice(invoiceWithId);
      
      return docRef.id;
    } catch (e) {
      // Fallback to local storage
      await _localService.saveInvoice(invoice);
      return invoice.id;
    }
  }

  // Update invoice
  Future<void> updateInvoice(String invoiceId, Invoice invoice) async {
    try {
      await _invoicesCollection.doc(invoiceId).update(invoice.toJson());
      
      // Also update locally
      await _localService.saveInvoice(invoice);
    } catch (e) {
      // Fallback to local storage
      await _localService.saveInvoice(invoice);
    }
  }

  // Delete invoice
  Future<void> deleteInvoice(String invoiceId) async {
    try {
      await _invoicesCollection.doc(invoiceId).delete();
      
      // Also delete locally
      await _localService.deleteInvoice(invoiceId);
    } catch (e) {
      // Fallback to local storage
      await _localService.deleteInvoice(invoiceId);
    }
  }

  // Get invoice by ID
  Future<Invoice?> getInvoiceById(String invoiceId) async {
    try {
      final doc = await _invoicesCollection.doc(invoiceId).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return Invoice.fromJson(data);
      }
      return null;
    } catch (e) {
      // Fallback to local storage
      return await _localService.getInvoiceById(invoiceId);
    }
  }

  // Get all invoices with optional filtering
  Future<List<Invoice>> getInvoices({
    InvoiceStatus? status,
    PaymentStatus? paymentStatus,
    String? clientId,
    DateTime? startDate,
    DateTime? endDate,
    String? searchTerm,
    int? limit,
    String? orderBy = 'createdAt',
    bool descending = true,
  }) async {
    try {
      Query query = _invoicesCollection;

      // Apply filters
      if (status != null) {
        query = query.where('status', isEqualTo: status.toString().split('.').last);
      }

      if (paymentStatus != null) {
        query = query.where('paymentStatus', isEqualTo: paymentStatus.toString().split('.').last);
      }

      if (clientId != null) {
        query = query.where('client.id', isEqualTo: clientId);
      }

      if (startDate != null) {
        query = query.where('issueDate', isGreaterThanOrEqualTo: startDate.toIso8601String());
      }

      if (endDate != null) {
        query = query.where('issueDate', isLessThanOrEqualTo: endDate.toIso8601String());
      }

      // Apply ordering
      if (orderBy != null) {
        query = query.orderBy(orderBy, descending: descending);
      }

      // Apply limit
      if (limit != null) {
        query = query.limit(limit);
      }

      final querySnapshot = await query.get();
      final invoices = querySnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return Invoice.fromJson(data);
      }).toList();

      // Apply search term filter (client-side)
      if (searchTerm != null && searchTerm.isNotEmpty) {
        final searchLower = searchTerm.toLowerCase();
        return invoices.where((invoice) {
          return invoice.invoiceNumber.toLowerCase().contains(searchLower) ||
                 invoice.client.name.toLowerCase().contains(searchLower) ||
                 invoice.client.email.toLowerCase().contains(searchLower);
        }).toList();
      }

      return invoices;
    } catch (e) {
      // Fallback to local storage
      return await _localService.searchInvoices(
        query: searchTerm,
        status: status,
        paymentStatus: paymentStatus,
        startDate: startDate,
        endDate: endDate,
      );
    }
  }

  // Create invoice from quotation
  Future<Invoice> createInvoiceFromQuotation(
    Quotation quotation, {
    DateTime? issueDate,
    DateTime? dueDate,
    InvoiceTemplate template = InvoiceTemplate.modern,
  }) async {
    final invoice = await _localService.createInvoiceFromQuotation(
      quotation,
      issueDate: issueDate,
      dueDate: dueDate,
      template: template,
    );

    // Save to Firebase
    await createInvoice(invoice);
    
    return invoice;
  }

  // Generate invoice number
  Future<String> generateInvoiceNumber() async {
    return await _localService.generateInvoiceNumber();
  }

  // Update invoice status
  Future<void> updateInvoiceStatus(String invoiceId, InvoiceStatus status) async {
    try {
      await _invoicesCollection.doc(invoiceId).update({
        'status': status.toString().split('.').last,
        'updatedAt': DateTime.now().toIso8601String(),
      });
      
      // Also update locally
      await _localService.updateInvoiceStatus(invoiceId, status);
    } catch (e) {
      // Fallback to local storage
      await _localService.updateInvoiceStatus(invoiceId, status);
    }
  }

  // Update payment status
  Future<void> updatePaymentStatus(String invoiceId, PaymentStatus paymentStatus) async {
    try {
      await _invoicesCollection.doc(invoiceId).update({
        'paymentStatus': paymentStatus.toString().split('.').last,
        'updatedAt': DateTime.now().toIso8601String(),
      });
      
      // Also update locally
      await _localService.updatePaymentStatus(invoiceId, paymentStatus);
    } catch (e) {
      // Fallback to local storage
      await _localService.updatePaymentStatus(invoiceId, paymentStatus);
    }
  }

  // Add payment record
  Future<void> addPayment(String invoiceId, PaymentRecord payment) async {
    try {
      final invoice = await getInvoiceById(invoiceId);
      if (invoice != null) {
        final updatedPayments = List<PaymentRecord>.from(invoice.payments)..add(payment);
        
        // Calculate new payment status
        final totalPaid = updatedPayments.fold(0.0, (sum, p) => sum + p.amount);
        PaymentStatus newPaymentStatus;
        
        if (totalPaid >= invoice.totalAmount) {
          newPaymentStatus = PaymentStatus.paid;
        } else if (totalPaid > 0) {
          newPaymentStatus = PaymentStatus.partial;
        } else {
          newPaymentStatus = PaymentStatus.pending;
        }
        
        await _invoicesCollection.doc(invoiceId).update({
          'payments': updatedPayments.map((p) => p.toJson()).toList(),
          'paymentStatus': newPaymentStatus.toString().split('.').last,
          'status': newPaymentStatus == PaymentStatus.paid 
              ? InvoiceStatus.paid.toString().split('.').last 
              : invoice.status.toString().split('.').last,
          'updatedAt': DateTime.now().toIso8601String(),
        });
        
        // Also update locally
        await _localService.addPayment(invoiceId, payment);
      }
    } catch (e) {
      // Fallback to local storage
      await _localService.addPayment(invoiceId, payment);
    }
  }

  // Get invoice statistics
  Future<Map<String, dynamic>> getInvoiceStatistics() async {
    try {
      final invoices = await getInvoices();
      
      final totalInvoices = invoices.length;
      final paidInvoices = invoices.where((inv) => inv.isPaid).length;
      final overdueInvoices = invoices.where((inv) => inv.isOverdue).length;
      final draftInvoices = invoices.where((inv) => inv.status == InvoiceStatus.draft).length;
      
      final totalAmount = invoices.fold(0.0, (sum, inv) => sum + inv.totalAmount);
      final paidAmount = invoices.fold(0.0, (sum, inv) => sum + inv.paidAmount);
      final pendingAmount = totalAmount - paidAmount;
      
      return {
        'totalInvoices': totalInvoices,
        'paidInvoices': paidInvoices,
        'overdueInvoices': overdueInvoices,
        'draftInvoices': draftInvoices,
        'totalAmount': totalAmount,
        'paidAmount': paidAmount,
        'pendingAmount': pendingAmount,
      };
    } catch (e) {
      // Fallback to local storage
      return await _localService.getInvoiceStatistics();
    }
  }

  // Get overdue invoices
  Future<List<Invoice>> getOverdueInvoices() async {
    try {
      final now = DateTime.now();
      final query = _invoicesCollection
          .where('dueDate', isLessThan: now.toIso8601String())
          .where('paymentStatus', whereNotIn: ['paid']);
      
      final querySnapshot = await query.get();
      return querySnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return Invoice.fromJson(data);
      }).toList();
    } catch (e) {
      // Fallback to local storage
      return await _localService.getOverdueInvoices();
    }
  }
}
