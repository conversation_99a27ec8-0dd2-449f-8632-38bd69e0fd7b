import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../blocs/invoice/invoice_bloc.dart';
import '../../blocs/invoice/invoice_event.dart';
import '../../blocs/invoice/invoice_state.dart';
import '../../blocs/clients/clients_bloc.dart';
import '../../models/invoice_models.dart';
import '../../models/quotation_models.dart';
import '../widgets/loading_widget.dart';

class CreateInvoiceScreen extends StatefulWidget {
  final Invoice? invoice; // For editing
  final Quotation? fromQuotation; // For creating from quotation

  const CreateInvoiceScreen({
    super.key,
    this.invoice,
    this.fromQuotation,
  });

  @override
  State<CreateInvoiceScreen> createState() => _CreateInvoiceScreenState();
}

class _CreateInvoiceScreenState extends State<CreateInvoiceScreen> {
  final _formKey = GlobalKey<FormState>();
  final _invoiceNumberController = TextEditingController();
  final _notesController = TextEditingController();
  final _termsController = TextEditingController();
  final _taxRateController = TextEditingController();
  final _shippingCostController = TextEditingController();
  final _additionalChargesController = TextEditingController();

  Client? _selectedClient;
  DateTime _issueDate = DateTime.now();
  DateTime _dueDate = DateTime.now().add(const Duration(days: 30));
  InvoiceTemplate _selectedTemplate = InvoiceTemplate.modern;
  List<InvoiceItem> _items = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
    context.read<ClientsBloc>().add(const LoadClients());
  }

  @override
  void dispose() {
    _invoiceNumberController.dispose();
    _notesController.dispose();
    _termsController.dispose();
    _taxRateController.dispose();
    _shippingCostController.dispose();
    _additionalChargesController.dispose();
    super.dispose();
  }

  void _initializeForm() {
    if (widget.invoice != null) {
      // Editing existing invoice
      final invoice = widget.invoice!;
      _invoiceNumberController.text = invoice.invoiceNumber;
      _selectedClient = invoice.client;
      _issueDate = invoice.issueDate;
      _dueDate = invoice.dueDate;
      _selectedTemplate = invoice.template;
      _items = List.from(invoice.items);
      _notesController.text = invoice.notes;
      _termsController.text = invoice.termsAndConditions;
      _taxRateController.text = invoice.taxRate.toString();
      _shippingCostController.text = invoice.shippingCost.toString();
      _additionalChargesController.text = invoice.additionalCharges.toString();
    } else if (widget.fromQuotation != null) {
      // Creating from quotation
      final quotation = widget.fromQuotation!;
      _selectedClient = quotation.client;
      _items = quotation.items.map((item) => InvoiceItem.fromQuotationItem(item)).toList();
      _notesController.text = quotation.notes;
      _termsController.text = quotation.termsAndConditions;
      _taxRateController.text = quotation.taxRate.toString();
      _shippingCostController.text = quotation.shippingCost.toString();
      _additionalChargesController.text = quotation.additionalCharges.toString();
    } else {
      // New invoice
      _taxRateController.text = '18.0';
      _shippingCostController.text = '0.0';
      _additionalChargesController.text = '0.0';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.invoice != null ? 'Edit Invoice' : 'Create Invoice'),
        actions: [
          TextButton(
            onPressed: _saveAsDraft,
            child: const Text('Save Draft'),
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: BlocListener<InvoiceBloc, InvoiceState>(
        listener: (context, state) {
          if (state is InvoiceCreated || state is InvoiceUpdated) {
            Navigator.of(context).pop();
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state is InvoiceCreated ? 'Invoice created successfully' : 'Invoice updated successfully'),
                backgroundColor: Colors.green,
              ),
            );
          } else if (state is InvoiceError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: LoadingOverlay(
          isLoading: _isLoading,
          loadingMessage: 'Saving invoice...',
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Basic Information Section
                        _buildBasicInfoSection(),
                        const SizedBox(height: 24),
                        
                        // Client Selection Section
                        _buildClientSection(),
                        const SizedBox(height: 24),
                        
                        // Items Section
                        _buildItemsSection(),
                        const SizedBox(height: 24),
                        
                        // Calculations Section
                        _buildCalculationsSection(),
                        const SizedBox(height: 24),
                        
                        // Template Selection
                        _buildTemplateSection(),
                        const SizedBox(height: 24),
                        
                        // Notes and Terms Section
                        _buildNotesAndTermsSection(),
                      ],
                    ),
                  ),
                ),
                
                // Bottom Action Bar
                _buildBottomActionBar(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Basic Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Invoice Number
            TextFormField(
              controller: _invoiceNumberController,
              decoration: const InputDecoration(
                labelText: 'Invoice Number',
                hintText: 'Auto-generated if empty',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                // Optional validation - will auto-generate if empty
                return null;
              },
            ),
            const SizedBox(height: 16),
            
            // Dates
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () => _selectDate(context, true),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'Issue Date',
                        border: OutlineInputBorder(),
                      ),
                      child: Text(
                        DateFormat('MMM dd, yyyy').format(_issueDate),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InkWell(
                    onTap: () => _selectDate(context, false),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'Due Date',
                        border: OutlineInputBorder(),
                      ),
                      child: Text(
                        DateFormat('MMM dd, yyyy').format(_dueDate),
                        style: TextStyle(
                          color: _dueDate.isBefore(DateTime.now()) ? Colors.red : null,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildClientSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Client Information',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: _showClientSelector,
                  icon: const Icon(Icons.person_search),
                  label: const Text('Select Client'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (_selectedClient != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Theme.of(context).primaryColor.withOpacity(0.3),
                  ),
                ),
                child: Row(
                  children: [
                    CircleAvatar(
                      child: Text(_selectedClient!.name[0].toUpperCase()),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _selectedClient!.name,
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(_selectedClient!.email),
                          if (_selectedClient!.phone.isNotEmpty)
                            Text(_selectedClient!.phone),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        setState(() {
                          _selectedClient = null;
                        });
                      },
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),
            ] else ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.withOpacity(0.3)),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.person_outline, color: Colors.grey),
                    SizedBox(width: 12),
                    Text(
                      'No client selected',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildItemsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Items',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: _addItem,
                  icon: const Icon(Icons.add),
                  label: const Text('Add Item'),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (_items.isEmpty) ...[
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.withOpacity(0.3)),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: Column(
                    children: [
                      Icon(Icons.inventory_2_outlined, size: 48, color: Colors.grey),
                      SizedBox(height: 8),
                      Text(
                        'No items added',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ),
            ] else ...[
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _items.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final item = _items[index];
                  return _buildItemTile(item, index);
                },
              ),
              const SizedBox(height: 16),
              _buildItemsSummary(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildItemTile(InvoiceItem item, int index) {
    final currencyFormat = NumberFormat.currency(symbol: '₹');

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  item.description,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              PopupMenuButton<String>(
                onSelected: (value) {
                  if (value == 'edit') {
                    _editItem(index);
                  } else if (value == 'delete') {
                    _deleteItem(index);
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: ListTile(
                      leading: Icon(Icons.edit),
                      title: Text('Edit'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: ListTile(
                      leading: Icon(Icons.delete, color: Colors.red),
                      title: Text('Delete'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text('Qty: ${item.quantity} ${item.unit}'),
              const SizedBox(width: 16),
              Text('Rate: ${currencyFormat.format(item.unitPrice)}'),
              if (item.discount > 0) ...[
                const SizedBox(width: 16),
                Text('Discount: ${currencyFormat.format(item.discount)}'),
              ],
              const Spacer(),
              Text(
                'Total: ${currencyFormat.format(item.total)}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildItemsSummary() {
    final currencyFormat = NumberFormat.currency(symbol: '₹');
    final subtotal = _items.fold<double>(0, (sum, item) => sum + item.total);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Text(
            'Items Subtotal: ${currencyFormat.format(subtotal)}',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const Spacer(),
          Text(
            '${_items.length} item${_items.length != 1 ? 's' : ''}',
            style: TextStyle(color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildCalculationsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Calculations',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _taxRateController,
                    decoration: const InputDecoration(
                      labelText: 'Tax Rate (%)',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) return 'Required';
                      if (double.tryParse(value) == null) return 'Invalid number';
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _shippingCostController,
                    decoration: const InputDecoration(
                      labelText: 'Shipping Cost',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) return 'Required';
                      if (double.tryParse(value) == null) return 'Invalid number';
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            TextFormField(
              controller: _additionalChargesController,
              decoration: const InputDecoration(
                labelText: 'Additional Charges',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) return 'Required';
                if (double.tryParse(value) == null) return 'Invalid number';
                return null;
              },
            ),
            const SizedBox(height: 16),

            _buildTotalSummary(),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalSummary() {
    final currencyFormat = NumberFormat.currency(symbol: '₹');
    final subtotal = _items.fold<double>(0, (sum, item) => sum + item.total);
    final taxRate = double.tryParse(_taxRateController.text) ?? 0;
    final shippingCost = double.tryParse(_shippingCostController.text) ?? 0;
    final additionalCharges = double.tryParse(_additionalChargesController.text) ?? 0;
    final taxAmount = subtotal * (taxRate / 100);
    final total = subtotal + taxAmount + shippingCost + additionalCharges;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.3),
        ),
      ),
      child: Column(
        children: [
          _buildSummaryRow('Subtotal', currencyFormat.format(subtotal)),
          if (taxRate > 0)
            _buildSummaryRow('Tax (${taxRate.toStringAsFixed(1)}%)', currencyFormat.format(taxAmount)),
          if (shippingCost > 0)
            _buildSummaryRow('Shipping', currencyFormat.format(shippingCost)),
          if (additionalCharges > 0)
            _buildSummaryRow('Additional Charges', currencyFormat.format(additionalCharges)),
          const Divider(),
          _buildSummaryRow(
            'Total',
            currencyFormat.format(total),
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: isTotal ? 16 : 14,
              color: isTotal ? Theme.of(context).primaryColor : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Template Selection',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: InvoiceTemplate.values.map((template) {
                final isSelected = _selectedTemplate == template;
                return FilterChip(
                  label: Text(_getTemplateDisplayName(template)),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      _selectedTemplate = template;
                    });
                  },
                  selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
                  checkmarkColor: Theme.of(context).primaryColor,
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesAndTermsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notes & Terms',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Notes',
                hintText: 'Additional notes for the client...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),

            TextFormField(
              controller: _termsController,
              decoration: const InputDecoration(
                labelText: 'Terms & Conditions',
                hintText: 'Payment terms and conditions...',
                border: OutlineInputBorder(),
              ),
              maxLines: 4,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomActionBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _canSave() ? _saveInvoice : null,
              child: Text(widget.invoice != null ? 'Update Invoice' : 'Create Invoice'),
            ),
          ),
        ],
      ),
    );
  }

  // =================== HELPER METHODS ===================

  String _getTemplateDisplayName(InvoiceTemplate template) {
    switch (template) {
      case InvoiceTemplate.modern:
        return 'Modern';
      case InvoiceTemplate.classic:
        return 'Classic';
      case InvoiceTemplate.minimal:
        return 'Minimal';
      case InvoiceTemplate.professional:
        return 'Professional';
    }
  }

  bool _canSave() {
    return _selectedClient != null && _items.isNotEmpty && !_isLoading;
  }

  // =================== DATE SELECTION ===================

  Future<void> _selectDate(BuildContext context, bool isIssueDate) async {
    final initialDate = isIssueDate ? _issueDate : _dueDate;
    final firstDate = isIssueDate ? DateTime(2020) : _issueDate;
    final lastDate = DateTime.now().add(const Duration(days: 365));

    final picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
    );

    if (picked != null) {
      setState(() {
        if (isIssueDate) {
          _issueDate = picked;
          // Ensure due date is not before issue date
          if (_dueDate.isBefore(_issueDate)) {
            _dueDate = _issueDate.add(const Duration(days: 30));
          }
        } else {
          _dueDate = picked;
        }
      });
    }
  }

  // =================== CLIENT SELECTION ===================

  void _showClientSelector() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) {
          return Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Text(
                  'Select Client',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: BlocBuilder<ClientsBloc, ClientsState>(
                    builder: (context, state) {
                      if (state is ClientsLoading) {
                        return const LoadingWidget();
                      } else if (state is ClientsLoaded) {
                        return ListView.builder(
                          controller: scrollController,
                          itemCount: state.clients.length,
                          itemBuilder: (context, index) {
                            final client = state.clients[index];
                            return ListTile(
                              leading: CircleAvatar(
                                child: Text(client.name[0].toUpperCase()),
                              ),
                              title: Text(client.name),
                              subtitle: Text(client.email),
                              onTap: () {
                                setState(() {
                                  _selectedClient = client;
                                });
                                Navigator.of(context).pop();
                              },
                            );
                          },
                        );
                      } else if (state is ClientsSearchResult) {
                        return ListView.builder(
                          controller: scrollController,
                          itemCount: state.clients.length,
                          itemBuilder: (context, index) {
                            final client = state.clients[index];
                            return ListTile(
                              leading: CircleAvatar(
                                child: Text(client.name[0].toUpperCase()),
                              ),
                              title: Text(client.name),
                              subtitle: Text(client.email),
                              onTap: () {
                                setState(() {
                                  _selectedClient = client;
                                });
                                Navigator.of(context).pop();
                              },
                            );
                          },
                        );
                      } else {
                        return const Center(
                          child: Text('Failed to load clients'),
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  // =================== ITEM MANAGEMENT ===================

  void _addItem() {
    _showItemDialog();
  }

  void _editItem(int index) {
    _showItemDialog(item: _items[index], index: index);
  }

  void _deleteItem(int index) {
    setState(() {
      _items.removeAt(index);
    });
  }

  void _showItemDialog({InvoiceItem? item, int? index}) {
    final isEditing = item != null;
    final descriptionController = TextEditingController(text: item?.description ?? '');
    final quantityController = TextEditingController(text: item?.quantity.toString() ?? '1');
    final unitController = TextEditingController(text: item?.unit ?? 'pcs');
    final unitPriceController = TextEditingController(text: item?.unitPrice.toString() ?? '');
    final discountController = TextEditingController(text: item?.discount.toString() ?? '0');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isEditing ? 'Edit Item' : 'Add Item'),
        content: SizedBox(
          width: double.maxFinite,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: quantityController,
                        decoration: const InputDecoration(
                          labelText: 'Quantity',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: unitController,
                        decoration: const InputDecoration(
                          labelText: 'Unit',
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: unitPriceController,
                        decoration: const InputDecoration(
                          labelText: 'Unit Price',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: discountController,
                        decoration: const InputDecoration(
                          labelText: 'Discount',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final newItem = InvoiceItem(
                id: DateTime.now().millisecondsSinceEpoch.toString(),
                description: descriptionController.text,
                quantity: double.tryParse(quantityController.text) ?? 1,
                unit: unitController.text,
                unitPrice: double.tryParse(unitPriceController.text) ?? 0,
                discount: double.tryParse(discountController.text) ?? 0,
              );

              setState(() {
                if (isEditing && index != null) {
                  _items[index] = newItem;
                } else {
                  _items.add(newItem);
                }
              });

              Navigator.of(context).pop();
            },
            child: Text(isEditing ? 'Update' : 'Add'),
          ),
        ],
      ),
    );
  }

  // =================== SAVE METHODS ===================

  void _saveAsDraft() {
    if (_selectedClient == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a client'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    _saveInvoice(isDraft: true);
  }

  void _saveInvoice({bool isDraft = false}) {
    if (!_formKey.currentState!.validate()) return;
    if (!_canSave() && !isDraft) return;

    setState(() {
      _isLoading = true;
    });

    final invoice = Invoice(
      id: widget.invoice?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      invoiceNumber: _invoiceNumberController.text.isEmpty
          ? '' // Will be auto-generated
          : _invoiceNumberController.text,
      client: _selectedClient!,
      issueDate: _issueDate,
      dueDate: _dueDate,
      items: _items,
      status: isDraft ? InvoiceStatus.draft : InvoiceStatus.sent,
      paymentStatus: PaymentStatus.pending,
      template: _selectedTemplate,
      notes: _notesController.text,
      termsAndConditions: _termsController.text,
      taxRate: double.tryParse(_taxRateController.text) ?? 0,
      shippingCost: double.tryParse(_shippingCostController.text) ?? 0,
      additionalCharges: double.tryParse(_additionalChargesController.text) ?? 0,
      createdAt: widget.invoice?.createdAt ?? DateTime.now(),
      payments: widget.invoice?.payments ?? [],
      companyDetails: widget.invoice?.companyDetails,
    );

    if (widget.invoice != null) {
      context.read<InvoiceBloc>().add(UpdateInvoice(
        invoiceId: widget.invoice!.id,
        invoice: invoice,
      ));
    } else {
      context.read<InvoiceBloc>().add(CreateInvoice(invoice));
    }
  }
}
