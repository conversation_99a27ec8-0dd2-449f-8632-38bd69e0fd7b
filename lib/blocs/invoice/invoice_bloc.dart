import 'package:flutter_bloc/flutter_bloc.dart';
import '../../models/invoice_models.dart';
import '../../repositories/invoice_repository.dart';
import 'invoice_event.dart';
import 'invoice_state.dart';

class InvoiceBloc extends Bloc<InvoiceEvent, InvoiceState> {
  final InvoiceRepository _repository;

  InvoiceBloc({InvoiceRepository? repository})
      : _repository = repository ?? InvoiceRepository(),
        super(const InvoiceInitial()) {
    
    // =================== LOADING EVENTS ===================
    on<LoadInvoices>(_onLoadInvoices);
    on<LoadInvoiceById>(_onLoadInvoiceById);
    on<LoadInvoiceStatistics>(_onLoadInvoiceStatistics);
    on<LoadOverdueInvoices>(_onLoadOverdueInvoices);
    
    // =================== CRUD EVENTS ===================
    on<CreateInvoice>(_onCreateInvoice);
    on<CreateInvoiceFromQuotation>(_onCreateInvoiceFromQuotation);
    on<UpdateInvoice>(_onUpdateInvoice);
    on<DeleteInvoice>(_onDeleteInvoice);
    
    // =================== STATUS EVENTS ===================
    on<UpdateInvoiceStatus>(_onUpdateInvoiceStatus);
    on<UpdatePaymentStatus>(_onUpdatePaymentStatus);
    on<MarkInvoiceAsSent>(_onMarkInvoiceAsSent);
    on<MarkInvoiceAsPaid>(_onMarkInvoiceAsPaid);
    on<CancelInvoice>(_onCancelInvoice);
    
    // =================== PAYMENT EVENTS ===================
    on<AddPayment>(_onAddPayment);
    
    // =================== SEARCH & FILTER EVENTS ===================
    on<SearchInvoices>(_onSearchInvoices);
    on<FilterInvoicesByStatus>(_onFilterInvoicesByStatus);
    on<FilterInvoicesByPaymentStatus>(_onFilterInvoicesByPaymentStatus);
    on<FilterInvoicesByDateRange>(_onFilterInvoicesByDateRange);
    on<ClearInvoiceFilters>(_onClearInvoiceFilters);
    
    // =================== BULK OPERATIONS ===================
    on<BulkUpdateInvoiceStatus>(_onBulkUpdateInvoiceStatus);
    on<BulkDeleteInvoices>(_onBulkDeleteInvoices);
    
    // =================== UTILITY EVENTS ===================
    on<RefreshInvoices>(_onRefreshInvoices);
    on<ResetInvoiceState>(_onResetInvoiceState);
    on<GenerateInvoiceNumber>(_onGenerateInvoiceNumber);
  }

  // =================== LOADING EVENT HANDLERS ===================

  Future<void> _onLoadInvoices(LoadInvoices event, Emitter<InvoiceState> emit) async {
    emit(const InvoiceLoading());
    
    try {
      final invoices = await _repository.getInvoices(
        status: event.status,
        paymentStatus: event.paymentStatus,
        searchTerm: event.searchTerm,
        startDate: event.startDate,
        endDate: event.endDate,
      );
      
      if (invoices.isEmpty) {
        emit(const InvoiceEmpty());
      } else {
        emit(InvoicesLoaded(
          invoices: invoices,
          filteredInvoices: invoices,
          statusFilter: event.status,
          paymentStatusFilter: event.paymentStatus,
          searchTerm: event.searchTerm,
          startDateFilter: event.startDate,
          endDateFilter: event.endDate,
        ));
      }
    } catch (e) {
      emit(InvoiceError(message: 'Failed to load invoices: ${e.toString()}'));
    }
  }

  Future<void> _onLoadInvoiceById(LoadInvoiceById event, Emitter<InvoiceState> emit) async {
    emit(const InvoiceLoading());
    
    try {
      final invoice = await _repository.getInvoiceById(event.invoiceId);
      
      if (invoice != null) {
        emit(InvoiceLoaded(invoice));
      } else {
        emit(const InvoiceError(message: 'Invoice not found'));
      }
    } catch (e) {
      emit(InvoiceError(message: 'Failed to load invoice: ${e.toString()}'));
    }
  }

  Future<void> _onLoadInvoiceStatistics(LoadInvoiceStatistics event, Emitter<InvoiceState> emit) async {
    emit(const InvoiceLoading());
    
    try {
      final statistics = await _repository.getInvoiceStatistics();
      emit(InvoiceStatisticsLoaded(statistics));
    } catch (e) {
      emit(InvoiceError(message: 'Failed to load statistics: ${e.toString()}'));
    }
  }

  Future<void> _onLoadOverdueInvoices(LoadOverdueInvoices event, Emitter<InvoiceState> emit) async {
    emit(const InvoiceLoading());
    
    try {
      final overdueInvoices = await _repository.getOverdueInvoices();
      emit(OverdueInvoicesLoaded(overdueInvoices));
    } catch (e) {
      emit(InvoiceError(message: 'Failed to load overdue invoices: ${e.toString()}'));
    }
  }

  // =================== CRUD EVENT HANDLERS ===================

  Future<void> _onCreateInvoice(CreateInvoice event, Emitter<InvoiceState> emit) async {
    emit(const InvoiceOperationLoading('Creating invoice'));
    
    try {
      final invoiceId = await _repository.createInvoice(event.invoice);
      final createdInvoice = event.invoice.copyWith(id: invoiceId);
      
      emit(InvoiceCreated(invoice: createdInvoice));
      
      // Refresh the invoices list
      add(const RefreshInvoices());
    } catch (e) {
      emit(InvoiceOperationError(
        operation: 'create',
        message: 'Failed to create invoice: ${e.toString()}',
      ));
    }
  }

  Future<void> _onCreateInvoiceFromQuotation(CreateInvoiceFromQuotation event, Emitter<InvoiceState> emit) async {
    emit(const InvoiceOperationLoading('Creating invoice from quotation'));
    
    try {
      final invoice = await _repository.createInvoiceFromQuotation(
        event.quotation,
        issueDate: event.issueDate,
        dueDate: event.dueDate,
        template: event.template,
      );
      
      emit(InvoiceCreated(
        invoice: invoice,
        message: 'Invoice created from quotation successfully',
      ));
      
      // Refresh the invoices list
      add(const RefreshInvoices());
    } catch (e) {
      emit(InvoiceOperationError(
        operation: 'create_from_quotation',
        message: 'Failed to create invoice from quotation: ${e.toString()}',
      ));
    }
  }

  Future<void> _onUpdateInvoice(UpdateInvoice event, Emitter<InvoiceState> emit) async {
    emit(const InvoiceOperationLoading('Updating invoice'));
    
    try {
      await _repository.updateInvoice(event.invoiceId, event.invoice);
      
      emit(InvoiceUpdated(invoice: event.invoice));
      
      // Refresh the invoices list
      add(const RefreshInvoices());
    } catch (e) {
      emit(InvoiceOperationError(
        operation: 'update',
        message: 'Failed to update invoice: ${e.toString()}',
      ));
    }
  }

  Future<void> _onDeleteInvoice(DeleteInvoice event, Emitter<InvoiceState> emit) async {
    emit(const InvoiceOperationLoading('Deleting invoice'));
    
    try {
      await _repository.deleteInvoice(event.invoiceId);
      
      emit(InvoiceDeleted(invoiceId: event.invoiceId));
      
      // Refresh the invoices list
      add(const RefreshInvoices());
    } catch (e) {
      emit(InvoiceOperationError(
        operation: 'delete',
        message: 'Failed to delete invoice: ${e.toString()}',
      ));
    }
  }

  // =================== STATUS EVENT HANDLERS ===================

  Future<void> _onUpdateInvoiceStatus(UpdateInvoiceStatus event, Emitter<InvoiceState> emit) async {
    emit(const InvoiceOperationLoading('Updating invoice status'));
    
    try {
      await _repository.updateInvoiceStatus(event.invoiceId, event.status);
      
      emit(InvoiceStatusUpdated(
        invoiceId: event.invoiceId,
        status: event.status,
      ));
      
      // Refresh the invoices list
      add(const RefreshInvoices());
    } catch (e) {
      emit(InvoiceOperationError(
        operation: 'update_status',
        message: 'Failed to update invoice status: ${e.toString()}',
      ));
    }
  }

  Future<void> _onUpdatePaymentStatus(UpdatePaymentStatus event, Emitter<InvoiceState> emit) async {
    emit(const InvoiceOperationLoading('Updating payment status'));
    
    try {
      await _repository.updatePaymentStatus(event.invoiceId, event.paymentStatus);
      
      emit(PaymentStatusUpdated(
        invoiceId: event.invoiceId,
        paymentStatus: event.paymentStatus,
      ));
      
      // Refresh the invoices list
      add(const RefreshInvoices());
    } catch (e) {
      emit(InvoiceOperationError(
        operation: 'update_payment_status',
        message: 'Failed to update payment status: ${e.toString()}',
      ));
    }
  }

  Future<void> _onMarkInvoiceAsSent(MarkInvoiceAsSent event, Emitter<InvoiceState> emit) async {
    emit(const InvoiceOperationLoading('Marking invoice as sent'));
    
    try {
      await _repository.markAsSent(event.invoiceId);
      
      emit(InvoiceStatusUpdated(
        invoiceId: event.invoiceId,
        status: InvoiceStatus.sent,
        message: 'Invoice marked as sent successfully',
      ));
      
      // Refresh the invoices list
      add(const RefreshInvoices());
    } catch (e) {
      emit(InvoiceOperationError(
        operation: 'mark_as_sent',
        message: 'Failed to mark invoice as sent: ${e.toString()}',
      ));
    }
  }

  Future<void> _onMarkInvoiceAsPaid(MarkInvoiceAsPaid event, Emitter<InvoiceState> emit) async {
    emit(const InvoiceOperationLoading('Marking invoice as paid'));

    try {
      await _repository.markAsPaid(event.invoiceId, paymentRecord: event.paymentRecord);

      emit(InvoiceStatusUpdated(
        invoiceId: event.invoiceId,
        status: InvoiceStatus.paid,
        message: 'Invoice marked as paid successfully',
      ));

      // Refresh the invoices list
      add(const RefreshInvoices());
    } catch (e) {
      emit(InvoiceOperationError(
        operation: 'mark_as_paid',
        message: 'Failed to mark invoice as paid: ${e.toString()}',
      ));
    }
  }

  Future<void> _onCancelInvoice(CancelInvoice event, Emitter<InvoiceState> emit) async {
    emit(const InvoiceOperationLoading('Cancelling invoice'));

    try {
      await _repository.cancelInvoice(event.invoiceId);

      emit(InvoiceStatusUpdated(
        invoiceId: event.invoiceId,
        status: InvoiceStatus.cancelled,
        message: 'Invoice cancelled successfully',
      ));

      // Refresh the invoices list
      add(const RefreshInvoices());
    } catch (e) {
      emit(InvoiceOperationError(
        operation: 'cancel',
        message: 'Failed to cancel invoice: ${e.toString()}',
      ));
    }
  }

  // =================== PAYMENT EVENT HANDLERS ===================

  Future<void> _onAddPayment(AddPayment event, Emitter<InvoiceState> emit) async {
    emit(const InvoiceOperationLoading('Adding payment'));

    try {
      await _repository.addPayment(event.invoiceId, event.payment);

      emit(PaymentAdded(
        invoiceId: event.invoiceId,
        payment: event.payment,
      ));

      // Refresh the invoices list
      add(const RefreshInvoices());
    } catch (e) {
      emit(InvoiceOperationError(
        operation: 'add_payment',
        message: 'Failed to add payment: ${e.toString()}',
      ));
    }
  }

  // =================== SEARCH & FILTER EVENT HANDLERS ===================

  Future<void> _onSearchInvoices(SearchInvoices event, Emitter<InvoiceState> emit) async {
    if (state is InvoicesLoaded) {
      final currentState = state as InvoicesLoaded;
      final filteredInvoices = _applyFilters(
        currentState.invoices,
        searchTerm: event.searchTerm,
        statusFilter: currentState.statusFilter,
        paymentStatusFilter: currentState.paymentStatusFilter,
        startDateFilter: currentState.startDateFilter,
        endDateFilter: currentState.endDateFilter,
      );

      emit(currentState.copyWith(
        filteredInvoices: filteredInvoices,
        searchTerm: event.searchTerm,
      ));
    }
  }

  Future<void> _onFilterInvoicesByStatus(FilterInvoicesByStatus event, Emitter<InvoiceState> emit) async {
    if (state is InvoicesLoaded) {
      final currentState = state as InvoicesLoaded;
      final filteredInvoices = _applyFilters(
        currentState.invoices,
        searchTerm: currentState.searchTerm,
        statusFilter: event.status,
        paymentStatusFilter: currentState.paymentStatusFilter,
        startDateFilter: currentState.startDateFilter,
        endDateFilter: currentState.endDateFilter,
      );

      emit(currentState.copyWith(
        filteredInvoices: filteredInvoices,
        statusFilter: event.status,
        clearStatusFilter: event.status == null,
      ));
    }
  }

  Future<void> _onFilterInvoicesByPaymentStatus(FilterInvoicesByPaymentStatus event, Emitter<InvoiceState> emit) async {
    if (state is InvoicesLoaded) {
      final currentState = state as InvoicesLoaded;
      final filteredInvoices = _applyFilters(
        currentState.invoices,
        searchTerm: currentState.searchTerm,
        statusFilter: currentState.statusFilter,
        paymentStatusFilter: event.paymentStatus,
        startDateFilter: currentState.startDateFilter,
        endDateFilter: currentState.endDateFilter,
      );

      emit(currentState.copyWith(
        filteredInvoices: filteredInvoices,
        paymentStatusFilter: event.paymentStatus,
        clearPaymentStatusFilter: event.paymentStatus == null,
      ));
    }
  }

  Future<void> _onFilterInvoicesByDateRange(FilterInvoicesByDateRange event, Emitter<InvoiceState> emit) async {
    if (state is InvoicesLoaded) {
      final currentState = state as InvoicesLoaded;
      final filteredInvoices = _applyFilters(
        currentState.invoices,
        searchTerm: currentState.searchTerm,
        statusFilter: currentState.statusFilter,
        paymentStatusFilter: currentState.paymentStatusFilter,
        startDateFilter: event.startDate,
        endDateFilter: event.endDate,
      );

      emit(currentState.copyWith(
        filteredInvoices: filteredInvoices,
        startDateFilter: event.startDate,
        endDateFilter: event.endDate,
        clearStartDateFilter: event.startDate == null,
        clearEndDateFilter: event.endDate == null,
      ));
    }
  }

  Future<void> _onClearInvoiceFilters(ClearInvoiceFilters event, Emitter<InvoiceState> emit) async {
    if (state is InvoicesLoaded) {
      final currentState = state as InvoicesLoaded;

      emit(currentState.copyWith(
        filteredInvoices: currentState.invoices,
        clearStatusFilter: true,
        clearPaymentStatusFilter: true,
        clearSearchTerm: true,
        clearStartDateFilter: true,
        clearEndDateFilter: true,
      ));
    }
  }

  // =================== BULK OPERATION EVENT HANDLERS ===================

  Future<void> _onBulkUpdateInvoiceStatus(BulkUpdateInvoiceStatus event, Emitter<InvoiceState> emit) async {
    emit(const InvoiceOperationLoading('Updating invoice statuses'));

    try {
      for (final invoiceId in event.invoiceIds) {
        await _repository.updateInvoiceStatus(invoiceId, event.status);
      }

      emit(BulkOperationCompleted(
        operation: 'bulk_status_update',
        affectedCount: event.invoiceIds.length,
        message: '${event.invoiceIds.length} invoices updated successfully',
      ));

      // Refresh the invoices list
      add(const RefreshInvoices());
    } catch (e) {
      emit(InvoiceOperationError(
        operation: 'bulk_status_update',
        message: 'Failed to update invoice statuses: ${e.toString()}',
      ));
    }
  }

  Future<void> _onBulkDeleteInvoices(BulkDeleteInvoices event, Emitter<InvoiceState> emit) async {
    emit(const InvoiceOperationLoading('Deleting invoices'));

    try {
      for (final invoiceId in event.invoiceIds) {
        await _repository.deleteInvoice(invoiceId);
      }

      emit(BulkOperationCompleted(
        operation: 'bulk_delete',
        affectedCount: event.invoiceIds.length,
        message: '${event.invoiceIds.length} invoices deleted successfully',
      ));

      // Refresh the invoices list
      add(const RefreshInvoices());
    } catch (e) {
      emit(InvoiceOperationError(
        operation: 'bulk_delete',
        message: 'Failed to delete invoices: ${e.toString()}',
      ));
    }
  }

  // =================== UTILITY EVENT HANDLERS ===================

  Future<void> _onRefreshInvoices(RefreshInvoices event, Emitter<InvoiceState> emit) async {
    if (state is InvoicesLoaded) {
      final currentState = state as InvoicesLoaded;
      add(LoadInvoices(
        status: currentState.statusFilter,
        paymentStatus: currentState.paymentStatusFilter,
        searchTerm: currentState.searchTerm,
        startDate: currentState.startDateFilter,
        endDate: currentState.endDateFilter,
      ));
    } else {
      add(const LoadInvoices());
    }
  }

  Future<void> _onResetInvoiceState(ResetInvoiceState event, Emitter<InvoiceState> emit) async {
    emit(const InvoiceInitial());
  }

  Future<void> _onGenerateInvoiceNumber(GenerateInvoiceNumber event, Emitter<InvoiceState> emit) async {
    try {
      final invoiceNumber = await _repository.generateInvoiceNumber();
      emit(InvoiceNumberGenerated(invoiceNumber));
    } catch (e) {
      emit(InvoiceError(message: 'Failed to generate invoice number: ${e.toString()}'));
    }
  }

  // =================== HELPER METHODS ===================

  List<Invoice> _applyFilters(
    List<Invoice> invoices, {
    String? searchTerm,
    InvoiceStatus? statusFilter,
    PaymentStatus? paymentStatusFilter,
    DateTime? startDateFilter,
    DateTime? endDateFilter,
  }) {
    return invoices.where((invoice) {
      // Search filter
      if (searchTerm != null && searchTerm.isNotEmpty) {
        final searchLower = searchTerm.toLowerCase();
        final matchesNumber = invoice.invoiceNumber.toLowerCase().contains(searchLower);
        final matchesClient = invoice.client.name.toLowerCase().contains(searchLower);
        final matchesEmail = invoice.client.email.toLowerCase().contains(searchLower);

        if (!matchesNumber && !matchesClient && !matchesEmail) {
          return false;
        }
      }

      // Status filter
      if (statusFilter != null && invoice.status != statusFilter) {
        return false;
      }

      // Payment status filter
      if (paymentStatusFilter != null && invoice.paymentStatus != paymentStatusFilter) {
        return false;
      }

      // Date range filter
      if (startDateFilter != null && invoice.issueDate.isBefore(startDateFilter)) {
        return false;
      }

      if (endDateFilter != null && invoice.issueDate.isAfter(endDateFilter)) {
        return false;
      }

      return true;
    }).toList();
  }
}
