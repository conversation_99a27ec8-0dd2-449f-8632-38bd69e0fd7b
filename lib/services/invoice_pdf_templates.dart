import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:qr/qr.dart';

import '../models/bank_models.dart';
import '../models/invoice_models.dart';
import 'payment_service.dart';

class InvoicePdfTemplates {
  // =================== CLASSIC TEMPLATE HELPERS ===================

  static pw.Widget buildClassicInvoiceHeader(Invoice invoice, DateFormat dateFormat) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.black, width: 2),
      ),
      child: pw.Column(
        children: [
          pw.Text(
            invoice.companyDetails?.name ?? 'AN ENTERPRISES',
            style: pw.TextStyle(
              fontSize: 24,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Text(
            'INVOICE',
            style: pw.TextStyle(
              fontSize: 20,
              fontWeight: pw.FontWeight.bold,
              letterSpacing: 3,
            ),
          ),
          pw.SizedBox(height: 15),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text('Invoice Number: ${invoice.invoiceNumber}'),
                  pw.Text('Issue Date: ${dateFormat.format(invoice.issueDate)}'),
                  pw.Text('Due Date: ${dateFormat.format(invoice.dueDate)}'),
                ],
              ),
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.end,
                children: [
                  pw.Text('Status: ${invoice.status.toString().split('.').last.toUpperCase()}'),
                  pw.Text('Payment: ${invoice.paymentStatus.toString().split('.').last.toUpperCase()}'),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget buildClassicClientDetails(Invoice invoice) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          child: pw.Container(
            padding: const pw.EdgeInsets.all(15),
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.black),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'BILL TO:',
                  style: pw.TextStyle(
                    fontSize: 12,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.SizedBox(height: 8),
                pw.Text(
                  invoice.client.name,
                  style: pw.TextStyle(
                    fontSize: 14,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.Text(invoice.client.email),
                if (invoice.client.phone.isNotEmpty) pw.Text(invoice.client.phone),
                if (invoice.client.address.isNotEmpty) pw.Text(invoice.client.address),
              ],
            ),
          ),
        ),
        pw.SizedBox(width: 20),
        pw.Expanded(
          child: pw.Container(
            padding: const pw.EdgeInsets.all(15),
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.black),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'FROM:',
                  style: pw.TextStyle(
                    fontSize: 12,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.SizedBox(height: 8),
                pw.Text(
                  invoice.companyDetails?.name ?? 'AN ENTERPRISES',
                  style: pw.TextStyle(
                    fontSize: 14,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                if (invoice.companyDetails?.email != null) pw.Text(invoice.companyDetails!.email),
                if (invoice.companyDetails?.phone != null) pw.Text(invoice.companyDetails!.phone),
                if (invoice.companyDetails?.address != null) pw.Text(invoice.companyDetails!.address),
              ],
            ),
          ),
        ),
      ],
    );
  }

  static pw.Widget buildClassicInvoiceItemsTable(Invoice invoice, NumberFormat currencyFormat) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.black),
      columnWidths: {
        0: const pw.FlexColumnWidth(3),
        1: const pw.FlexColumnWidth(1),
        2: const pw.FlexColumnWidth(1),
        3: const pw.FlexColumnWidth(1.5),
        4: const pw.FlexColumnWidth(1.5),
        5: const pw.FlexColumnWidth(2),
      },
      children: [
        // Header row
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey300),
          children: [
            buildClassicTableHeader('DESCRIPTION'),
            buildClassicTableHeader('QTY'),
            buildClassicTableHeader('UNIT'),
            buildClassicTableHeader('RATE'),
            buildClassicTableHeader('DISCOUNT'),
            buildClassicTableHeader('AMOUNT'),
          ],
        ),
        // Items
        ...invoice.items.map((item) {
          return pw.TableRow(
            children: [
              buildClassicTableCell(item.description),
              buildClassicTableCell(item.quantity.toString(), alignment: pw.Alignment.center),
              buildClassicTableCell(item.unit, alignment: pw.Alignment.center),
              buildClassicTableCell(currencyFormat.format(item.unitPrice), alignment: pw.Alignment.centerRight),
              buildClassicTableCell(currencyFormat.format(item.discount), alignment: pw.Alignment.centerRight),
              buildClassicTableCell(
                currencyFormat.format(item.totalPrice),
                alignment: pw.Alignment.centerRight,
                isBold: true,
              ),
            ],
          );
        }),
      ],
    );
  }

  static pw.Widget buildClassicTableHeader(String text) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: 10,
          fontWeight: pw.FontWeight.bold,
        ),
      ),
    );
  }

  static pw.Widget buildClassicTableCell(
    String text, {
    pw.Alignment alignment = pw.Alignment.centerLeft,
    bool isBold = false,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      alignment: alignment,
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: 9,
          fontWeight: isBold ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
      ),
    );
  }

  static pw.Widget buildClassicInvoiceTotalSummary(Invoice invoice, NumberFormat currencyFormat) {
    final subtotal = invoice.subtotalAmount;
    final taxAmount = invoice.taxAmount;
    final total = invoice.totalAmount;

    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.end,
      children: [
        pw.Container(
          width: 250,
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.black),
          ),
          child: pw.Column(
            children: [
              buildClassicSummaryRow('Subtotal', currencyFormat.format(subtotal)),
              
              if (invoice.taxRate > 0)
                buildClassicSummaryRow(
                  'Tax (${invoice.taxRate.toStringAsFixed(1)}%)',
                  currencyFormat.format(taxAmount),
                ),
              
              if (invoice.shippingCost > 0)
                buildClassicSummaryRow('Shipping', currencyFormat.format(invoice.shippingCost)),
              
              if (invoice.additionalCharges > 0)
                buildClassicSummaryRow('Additional Charges', currencyFormat.format(invoice.additionalCharges)),
              
              pw.Container(
                padding: const pw.EdgeInsets.all(10),
                decoration: const pw.BoxDecoration(
                  color: PdfColors.grey300,
                  border: pw.Border(top: pw.BorderSide(color: PdfColors.black, width: 2)),
                ),
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text(
                      'TOTAL',
                      style: pw.TextStyle(
                        fontSize: 14,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.Text(
                      currencyFormat.format(total),
                      style: pw.TextStyle(
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  static pw.Widget buildClassicSummaryRow(String label, String value) {
    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      decoration: const pw.BoxDecoration(
        border: pw.Border(bottom: pw.BorderSide(color: PdfColors.black)),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(label, style: const pw.TextStyle(fontSize: 11)),
          pw.Text(
            value,
            style: pw.TextStyle(
              fontSize: 11,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  // =================== MINIMAL TEMPLATE HELPERS ===================

  static pw.Widget buildMinimalInvoiceHeader(Invoice invoice, DateFormat dateFormat) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          invoice.companyDetails?.name ?? 'AN ENTERPRISES',
          style: pw.TextStyle(
            fontSize: 28,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.grey800,
          ),
        ),
        pw.SizedBox(height: 5),
        pw.Container(
          height: 2,
          width: 100,
          color: PdfColors.grey800,
        ),
        pw.SizedBox(height: 20),
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Text(
              'Invoice',
              style: pw.TextStyle(
                fontSize: 20,
                fontWeight: pw.FontWeight.normal,
                color: PdfColors.grey600,
              ),
            ),
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                pw.Text(
                  invoice.invoiceNumber,
                  style: pw.TextStyle(
                    fontSize: 16,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.Text(
                  dateFormat.format(invoice.issueDate),
                  style: const pw.TextStyle(
                    fontSize: 12,
                    color: PdfColors.grey600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }
}
