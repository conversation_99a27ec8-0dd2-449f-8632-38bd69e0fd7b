import 'package:equatable/equatable.dart';
import 'quotation_models.dart';

enum InvoiceStatus {
  draft,
  sent,
  paid,
  overdue,
  cancelled,
  refunded
}

enum InvoiceTemplate {
  modern,
  professional,
  creative,
  minimal,
  corporate
}

enum PaymentStatus {
  pending,
  partial,
  paid,
  overdue,
  cancelled
}

class InvoiceItem extends Equatable {
  final String id;
  final String description;
  final double quantity;
  final double unitPrice;
  final double discount;
  final String? unit;
  final String? category;

  const InvoiceItem({
    required this.id,
    required this.description,
    required this.quantity,
    required this.unitPrice,
    this.discount = 0.0,
    this.unit,
    this.category,
  });

  double get subtotal => quantity * unitPrice;
  double get discountAmount => subtotal * (discount / 100);
  double get total => subtotal - discountAmount;

  factory InvoiceItem.fromQuotationItem(QuotationItem quotationItem) {
    return InvoiceItem(
      id: quotationItem.id,
      description: quotationItem.description,
      quantity: quotationItem.quantity,
      unitPrice: quotationItem.unitPrice,
      discount: quotationItem.discount,
      unit: quotationItem.unit,
      category: quotationItem.category,
    );
  }

  InvoiceItem copyWith({
    String? id,
    String? description,
    double? quantity,
    double? unitPrice,
    double? discount,
    String? unit,
    String? category,
  }) {
    return InvoiceItem(
      id: id ?? this.id,
      description: description ?? this.description,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      discount: discount ?? this.discount,
      unit: unit ?? this.unit,
      category: category ?? this.category,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'description': description,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'discount': discount,
      'unit': unit,
      'category': category,
    };
  }

  factory InvoiceItem.fromJson(Map<String, dynamic> json) {
    return InvoiceItem(
      id: json['id'] as String,
      description: json['description'] as String,
      quantity: (json['quantity'] as num).toDouble(),
      unitPrice: (json['unitPrice'] as num).toDouble(),
      discount: (json['discount'] as num?)?.toDouble() ?? 0.0,
      unit: json['unit'] as String?,
      category: json['category'] as String?,
    );
  }

  @override
  List<Object?> get props => [
        id,
        description,
        quantity,
        unitPrice,
        discount,
        unit,
        category,
      ];
}

class PaymentRecord extends Equatable {
  final String id;
  final double amount;
  final DateTime paymentDate;
  final String paymentMethod;
  final String? transactionId;
  final String? notes;

  const PaymentRecord({
    required this.id,
    required this.amount,
    required this.paymentDate,
    required this.paymentMethod,
    this.transactionId,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'amount': amount,
      'paymentDate': paymentDate.toIso8601String(),
      'paymentMethod': paymentMethod,
      'transactionId': transactionId,
      'notes': notes,
    };
  }

  factory PaymentRecord.fromJson(Map<String, dynamic> json) {
    return PaymentRecord(
      id: json['id'] as String,
      amount: (json['amount'] as num).toDouble(),
      paymentDate: DateTime.parse(json['paymentDate'] as String),
      paymentMethod: json['paymentMethod'] as String,
      transactionId: json['transactionId'] as String?,
      notes: json['notes'] as String?,
    );
  }

  @override
  List<Object?> get props => [
        id,
        amount,
        paymentDate,
        paymentMethod,
        transactionId,
        notes,
      ];
}

class Invoice extends Equatable {
  final String id;
  final String invoiceNumber;
  final Client client;
  final DateTime issueDate;
  final DateTime dueDate;
  final List<InvoiceItem> items;
  final double taxRate;
  final double shippingCost;
  final double additionalCharges;
  final String notes;
  final String termsAndConditions;
  final InvoiceStatus status;
  final PaymentStatus paymentStatus;
  final InvoiceTemplate template;
  final String createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? quotationId;
  final List<PaymentRecord> payments;
  final CompanyDetails? companyDetails;

  const Invoice({
    required this.id,
    required this.invoiceNumber,
    required this.client,
    required this.issueDate,
    required this.dueDate,
    required this.items,
    this.taxRate = 0.0,
    this.shippingCost = 0.0,
    this.additionalCharges = 0.0,
    this.notes = '',
    this.termsAndConditions = '',
    this.status = InvoiceStatus.draft,
    this.paymentStatus = PaymentStatus.pending,
    this.template = InvoiceTemplate.modern,
    this.createdBy = 'Admin User',
    required this.createdAt,
    this.updatedAt,
    this.quotationId,
    this.payments = const [],
    this.companyDetails,
  });

  // Calculated properties
  double get subtotal => items.fold(0.0, (sum, item) => sum + item.total);
  double get totalDiscount => items.fold(0.0, (sum, item) => sum + item.discountAmount);
  double get taxAmount => subtotal * (taxRate / 100);
  double get totalAmount => subtotal + taxAmount + shippingCost + additionalCharges;
  double get paidAmount => payments.fold(0.0, (sum, payment) => sum + payment.amount);
  double get balanceAmount => totalAmount - paidAmount;

  bool get isOverdue => DateTime.now().isAfter(dueDate) && paymentStatus != PaymentStatus.paid;
  bool get isPaid => paymentStatus == PaymentStatus.paid;
  bool get isPartiallyPaid => paymentStatus == PaymentStatus.partial;

  String get statusText {
    switch (status) {
      case InvoiceStatus.draft:
        return 'Draft';
      case InvoiceStatus.sent:
        return 'Sent';
      case InvoiceStatus.paid:
        return 'Paid';
      case InvoiceStatus.overdue:
        return 'Overdue';
      case InvoiceStatus.cancelled:
        return 'Cancelled';
      case InvoiceStatus.refunded:
        return 'Refunded';
    }
  }

  String get paymentStatusText {
    switch (paymentStatus) {
      case PaymentStatus.pending:
        return 'Pending';
      case PaymentStatus.partial:
        return 'Partially Paid';
      case PaymentStatus.paid:
        return 'Paid';
      case PaymentStatus.overdue:
        return 'Overdue';
      case PaymentStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get templateName {
    switch (template) {
      case InvoiceTemplate.modern:
        return 'Modern';
      case InvoiceTemplate.professional:
        return 'Professional';
      case InvoiceTemplate.creative:
        return 'Creative';
      case InvoiceTemplate.minimal:
        return 'Minimal';
      case InvoiceTemplate.corporate:
        return 'Corporate';
    }
  }

  factory Invoice.fromQuotation(Quotation quotation, {
    required String invoiceNumber,
    required DateTime issueDate,
    required DateTime dueDate,
    InvoiceTemplate template = InvoiceTemplate.modern,
  }) {
    return Invoice(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      invoiceNumber: invoiceNumber,
      client: quotation.client,
      issueDate: issueDate,
      dueDate: dueDate,
      items: quotation.items.map((item) => InvoiceItem.fromQuotationItem(item)).toList(),
      taxRate: quotation.taxRate,
      shippingCost: quotation.shippingCost,
      notes: quotation.notes,
      template: template,
      createdBy: quotation.createdBy,
      createdAt: DateTime.now(),
      quotationId: quotation.id,
    );
  }

  Invoice copyWith({
    String? id,
    String? invoiceNumber,
    Client? client,
    DateTime? issueDate,
    DateTime? dueDate,
    List<InvoiceItem>? items,
    double? taxRate,
    double? shippingCost,
    double? additionalCharges,
    String? notes,
    String? termsAndConditions,
    InvoiceStatus? status,
    PaymentStatus? paymentStatus,
    InvoiceTemplate? template,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? quotationId,
    List<PaymentRecord>? payments,
    CompanyDetails? companyDetails,
  }) {
    return Invoice(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      client: client ?? this.client,
      issueDate: issueDate ?? this.issueDate,
      dueDate: dueDate ?? this.dueDate,
      items: items ?? this.items,
      taxRate: taxRate ?? this.taxRate,
      shippingCost: shippingCost ?? this.shippingCost,
      additionalCharges: additionalCharges ?? this.additionalCharges,
      notes: notes ?? this.notes,
      termsAndConditions: termsAndConditions ?? this.termsAndConditions,
      status: status ?? this.status,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      template: template ?? this.template,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      quotationId: quotationId ?? this.quotationId,
      payments: payments ?? this.payments,
      companyDetails: companyDetails ?? this.companyDetails,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'invoiceNumber': invoiceNumber,
      'client': client.toJson(),
      'issueDate': issueDate.toIso8601String(),
      'dueDate': dueDate.toIso8601String(),
      'items': items.map((item) => item.toJson()).toList(),
      'taxRate': taxRate,
      'shippingCost': shippingCost,
      'additionalCharges': additionalCharges,
      'notes': notes,
      'termsAndConditions': termsAndConditions,
      'status': status.toString().split('.').last,
      'paymentStatus': paymentStatus.toString().split('.').last,
      'template': template.toString().split('.').last,
      'createdBy': createdBy,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'quotationId': quotationId,
      'payments': payments.map((payment) => payment.toJson()).toList(),
      'companyDetails': companyDetails?.toJson(),
    };
  }

  factory Invoice.fromJson(Map<String, dynamic> json) {
    return Invoice(
      id: json['id'] as String,
      invoiceNumber: json['invoiceNumber'] as String,
      client: Client.fromJson(json['client'] as Map<String, dynamic>),
      issueDate: DateTime.parse(json['issueDate'] as String),
      dueDate: DateTime.parse(json['dueDate'] as String),
      items: (json['items'] as List)
          .map((item) => InvoiceItem.fromJson(item as Map<String, dynamic>))
          .toList(),
      taxRate: (json['taxRate'] as num?)?.toDouble() ?? 0.0,
      shippingCost: (json['shippingCost'] as num?)?.toDouble() ?? 0.0,
      additionalCharges: (json['additionalCharges'] as num?)?.toDouble() ?? 0.0,
      notes: json['notes'] as String? ?? '',
      termsAndConditions: json['termsAndConditions'] as String? ?? '',
      status: InvoiceStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => InvoiceStatus.draft,
      ),
      paymentStatus: PaymentStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['paymentStatus'],
        orElse: () => PaymentStatus.pending,
      ),
      template: InvoiceTemplate.values.firstWhere(
        (e) => e.toString().split('.').last == json['template'],
        orElse: () => InvoiceTemplate.modern,
      ),
      createdBy: json['createdBy'] as String? ?? 'Admin User',
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      quotationId: json['quotationId'] as String?,
      payments: (json['payments'] as List?)
          ?.map((payment) => PaymentRecord.fromJson(payment as Map<String, dynamic>))
          .toList() ?? [],
      companyDetails: json['companyDetails'] != null
          ? CompanyDetails.fromJson(json['companyDetails'] as Map<String, dynamic>)
          : null,
    );
  }

  @override
  List<Object?> get props => [
        id,
        invoiceNumber,
        client,
        issueDate,
        dueDate,
        items,
        taxRate,
        shippingCost,
        additionalCharges,
        notes,
        termsAndConditions,
        status,
        paymentStatus,
        template,
        createdBy,
        createdAt,
        updatedAt,
        quotationId,
        payments,
        companyDetails,
      ];
}

class Invoice extends Equatable {
  final String id;
  final String invoiceNumber;
  final Client client;
  final DateTime issueDate;
  final DateTime dueDate;
  final List<InvoiceItem> items;
  final double taxRate;
  final double shippingCost;
  final double additionalCharges;
  final String notes;
  final String termsAndConditions;
  final InvoiceStatus status;
  final PaymentStatus paymentStatus;
  final InvoiceTemplate template;
  final String createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? quotationId;
  final List<PaymentRecord> payments;
  final CompanyDetails? companyDetails;

  const Invoice({
    required this.id,
    required this.invoiceNumber,
    required this.client,
    required this.issueDate,
    required this.dueDate,
    required this.items,
    this.taxRate = 0.0,
    this.shippingCost = 0.0,
    this.additionalCharges = 0.0,
    this.notes = '',
    this.termsAndConditions = '',
    this.status = InvoiceStatus.draft,
    this.paymentStatus = PaymentStatus.pending,
    this.template = InvoiceTemplate.modern,
    this.createdBy = 'Admin User',
    required this.createdAt,
    this.updatedAt,
    this.quotationId,
    this.payments = const [],
    this.companyDetails,
  });

  // Calculated properties
  double get subtotal => items.fold(0.0, (sum, item) => sum + item.total);
  double get totalDiscount => items.fold(0.0, (sum, item) => sum + item.discountAmount);
  double get taxAmount => subtotal * (taxRate / 100);
  double get totalAmount => subtotal + taxAmount + shippingCost + additionalCharges;
  double get paidAmount => payments.fold(0.0, (sum, payment) => sum + payment.amount);
  double get balanceAmount => totalAmount - paidAmount;

  bool get isOverdue => DateTime.now().isAfter(dueDate) && paymentStatus != PaymentStatus.paid;
  bool get isPaid => paymentStatus == PaymentStatus.paid;
  bool get isPartiallyPaid => paymentStatus == PaymentStatus.partial;

  String get statusText {
    switch (status) {
      case InvoiceStatus.draft:
        return 'Draft';
      case InvoiceStatus.sent:
        return 'Sent';
      case InvoiceStatus.paid:
        return 'Paid';
      case InvoiceStatus.overdue:
        return 'Overdue';
      case InvoiceStatus.cancelled:
        return 'Cancelled';
      case InvoiceStatus.refunded:
        return 'Refunded';
    }
  }

  String get paymentStatusText {
    switch (paymentStatus) {
      case PaymentStatus.pending:
        return 'Pending';
      case PaymentStatus.partial:
        return 'Partially Paid';
      case PaymentStatus.paid:
        return 'Paid';
      case PaymentStatus.overdue:
        return 'Overdue';
      case PaymentStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get templateName {
    switch (template) {
      case InvoiceTemplate.modern:
        return 'Modern';
      case InvoiceTemplate.professional:
        return 'Professional';
      case InvoiceTemplate.creative:
        return 'Creative';
      case InvoiceTemplate.minimal:
        return 'Minimal';
      case InvoiceTemplate.corporate:
        return 'Corporate';
    }
  }

  factory Invoice.fromQuotation(Quotation quotation, {
    required String invoiceNumber,
    required DateTime issueDate,
    required DateTime dueDate,
    InvoiceTemplate template = InvoiceTemplate.modern,
  }) {
    return Invoice(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      invoiceNumber: invoiceNumber,
      client: quotation.client,
      issueDate: issueDate,
      dueDate: dueDate,
      items: quotation.items.map((item) => InvoiceItem.fromQuotationItem(item)).toList(),
      taxRate: quotation.taxRate,
      shippingCost: quotation.shippingCost,
      notes: quotation.notes,
      template: template,
      createdBy: quotation.createdBy,
      createdAt: DateTime.now(),
      quotationId: quotation.id,
    );
  }
