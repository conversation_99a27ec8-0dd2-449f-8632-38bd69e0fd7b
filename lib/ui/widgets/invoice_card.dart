import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../models/invoice_models.dart';

class InvoiceCard extends StatelessWidget {
  final Invoice invoice;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool isSelected;
  final bool isSelectionMode;
  final ValueChanged<bool>? onSelectionChanged;

  const InvoiceCard({
    super.key,
    required this.invoice,
    this.onTap,
    this.onLongPress,
    this.isSelected = false,
    this.isSelectionMode = false,
    this.onSelectionChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currencyFormat = NumberFormat.currency(symbol: '₹');
    final dateFormat = DateFormat('MMM dd, yyyy');

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isSelected ? 8 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isSelected
            ? BorderSide(color: theme.primaryColor, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                children: [
                  if (isSelectionMode) ...[
                    Checkbox(
                      value: isSelected,
                      onChanged: onSelectionChanged,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    const SizedBox(width: 8),
                  ],
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          invoice.invoiceNumber,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          invoice.client.name,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        currencyFormat.format(invoice.totalAmount),
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: _getAmountColor(context),
                        ),
                      ),
                      Text(
                        dateFormat.format(invoice.issueDate),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Status Row
              Row(
                children: [
                  _buildStatusChip(
                    context,
                    invoice.status.toString().split('.').last.toUpperCase(),
                    _getStatusColor(invoice.status),
                  ),
                  const SizedBox(width: 8),
                  _buildStatusChip(
                    context,
                    invoice.paymentStatus.toString().split('.').last.toUpperCase(),
                    _getPaymentStatusColor(invoice.paymentStatus),
                  ),
                  const Spacer(),
                  if (invoice.isOverdue)
                    Icon(
                      Icons.warning,
                      color: Colors.red[600],
                      size: 20,
                    ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Due Date Row
              Row(
                children: [
                  Icon(
                    Icons.schedule,
                    size: 16,
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Due: ${dateFormat.format(invoice.dueDate)}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: invoice.isOverdue ? Colors.red[600] : null,
                      fontWeight: invoice.isOverdue ? FontWeight.w500 : null,
                    ),
                  ),
                  const Spacer(),
                  if (invoice.payments.isNotEmpty) ...[
                    Icon(
                      Icons.payment,
                      size: 16,
                      color: Colors.green[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${invoice.payments.length} payment${invoice.payments.length > 1 ? 's' : ''}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.green[600],
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context, String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        label,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: color,
          fontWeight: FontWeight.w500,
          fontSize: 10,
        ),
      ),
    );
  }

  Color _getStatusColor(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return Colors.grey[600]!;
      case InvoiceStatus.sent:
        return Colors.blue[600]!;
      case InvoiceStatus.viewed:
        return Colors.orange[600]!;
      case InvoiceStatus.paid:
        return Colors.green[600]!;
      case InvoiceStatus.overdue:
        return Colors.red[600]!;
      case InvoiceStatus.cancelled:
        return Colors.red[800]!;
    }
  }

  Color _getPaymentStatusColor(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.pending:
        return Colors.orange[600]!;
      case PaymentStatus.partial:
        return Colors.blue[600]!;
      case PaymentStatus.paid:
        return Colors.green[600]!;
      case PaymentStatus.overdue:
        return Colors.red[600]!;
      case PaymentStatus.refunded:
        return Colors.purple[600]!;
    }
  }

  Color _getAmountColor(BuildContext context) {
    switch (invoice.paymentStatus) {
      case PaymentStatus.paid:
        return Colors.green[600]!;
      case PaymentStatus.overdue:
        return Colors.red[600]!;
      case PaymentStatus.partial:
        return Colors.blue[600]!;
      default:
        return Theme.of(context).colorScheme.onSurface;
    }
  }
}
