import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../blocs/invoice/invoice_bloc.dart';
import '../../blocs/invoice/invoice_event.dart';
import '../../blocs/invoice/invoice_state.dart';
import '../../models/invoice_models.dart';
import '../widgets/invoice_card.dart';
import '../widgets/invoice_filter_widget.dart';
import '../widgets/loading_widget.dart';
import '../widgets/empty_state_widget.dart';
import 'create_invoice_screen.dart';
import 'invoice_detail_screen.dart';

class InvoicesScreen extends StatefulWidget {
  const InvoicesScreen({super.key});

  @override
  State<InvoicesScreen> createState() => _InvoicesScreenState();
}

class _InvoicesScreenState extends State<InvoicesScreen> {
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  List<String> _selectedInvoiceIds = [];
  bool _isSelectionMode = false;

  @override
  void initState() {
    super.initState();
    context.read<InvoiceBloc>().add(const LoadInvoices());
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Column(
        children: [
          // Search and Filter Section
          _buildSearchAndFilterSection(),
          
          // Invoice List
          Expanded(
            child: BlocConsumer<InvoiceBloc, InvoiceState>(
              listener: (context, state) {
                if (state is InvoiceError) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.red,
                    ),
                  );
                } else if (state is InvoiceCreated) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.green,
                    ),
                  );
                } else if (state is InvoiceUpdated) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.green,
                    ),
                  );
                } else if (state is InvoiceDeleted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.orange,
                    ),
                  );
                  _exitSelectionMode();
                }
              },
              builder: (context, state) {
                if (state is InvoiceLoading) {
                  return const LoadingWidget();
                } else if (state is InvoicesLoaded) {
                  return _buildInvoicesList(state);
                } else if (state is InvoiceEmpty) {
                  return EmptyStateWidget(
                    icon: Icons.receipt_long,
                    title: 'No Invoices Found',
                    subtitle: state.message,
                    actionText: 'Create Invoice',
                    onActionPressed: () => _navigateToCreateInvoice(),
                  );
                } else if (state is InvoiceError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.red[300],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error Loading Invoices',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          state.message,
                          style: Theme.of(context).textTheme.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            context.read<InvoiceBloc>().add(const LoadInvoices());
                          },
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }
                
                return const SizedBox.shrink();
              },
            ),
          ),
        ],
      ),
      floatingActionButton: _isSelectionMode ? null : FloatingActionButton(
        onPressed: _navigateToCreateInvoice,
        child: const Icon(Icons.add),
      ),
      bottomNavigationBar: _isSelectionMode ? _buildSelectionBottomBar() : null,
    );
  }

  PreferredSizeWidget _buildAppBar() {
    if (_isSelectionMode) {
      return AppBar(
        title: Text('${_selectedInvoiceIds.length} selected'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: _exitSelectionMode,
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.select_all),
            onPressed: _selectAllInvoices,
          ),
        ],
      );
    }

    return AppBar(
      title: _isSearching
          ? TextField(
              controller: _searchController,
              autofocus: true,
              decoration: const InputDecoration(
                hintText: 'Search invoices...',
                border: InputBorder.none,
                hintStyle: TextStyle(color: Colors.white70),
              ),
              style: const TextStyle(color: Colors.white),
              onChanged: (value) {
                context.read<InvoiceBloc>().add(SearchInvoices(value));
              },
            )
          : const Text('Invoices'),
      actions: [
        if (_isSearching)
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () {
              _searchController.clear();
              context.read<InvoiceBloc>().add(const ClearInvoiceFilters());
              setState(() {
                _isSearching = false;
              });
            },
          )
        else
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              setState(() {
                _isSearching = true;
              });
            },
          ),
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: () {
            context.read<InvoiceBloc>().add(const RefreshInvoices());
          },
        ),
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'statistics',
              child: ListTile(
                leading: Icon(Icons.analytics),
                title: Text('Statistics'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'overdue',
              child: ListTile(
                leading: Icon(Icons.warning),
                title: Text('Overdue Invoices'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'export',
              child: ListTile(
                leading: Icon(Icons.download),
                title: Text('Export Data'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSearchAndFilterSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InvoiceFilterWidget(
        onStatusFilterChanged: (status) {
          context.read<InvoiceBloc>().add(FilterInvoicesByStatus(status));
        },
        onPaymentStatusFilterChanged: (paymentStatus) {
          context.read<InvoiceBloc>().add(FilterInvoicesByPaymentStatus(paymentStatus));
        },
        onDateRangeChanged: (startDate, endDate) {
          context.read<InvoiceBloc>().add(FilterInvoicesByDateRange(
            startDate: startDate,
            endDate: endDate,
          ));
        },
        onClearFilters: () {
          context.read<InvoiceBloc>().add(const ClearInvoiceFilters());
        },
      ),
    );
  }

  Widget _buildInvoicesList(InvoicesLoaded state) {
    if (state.filteredInvoices.isEmpty) {
      return EmptyStateWidget(
        icon: Icons.filter_list_off,
        title: 'No Invoices Match Filters',
        subtitle: 'Try adjusting your search criteria',
        actionText: 'Clear Filters',
        onActionPressed: () {
          context.read<InvoiceBloc>().add(const ClearInvoiceFilters());
        },
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        context.read<InvoiceBloc>().add(const RefreshInvoices());
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: state.filteredInvoices.length,
        itemBuilder: (context, index) {
          final invoice = state.filteredInvoices[index];
          final isSelected = _selectedInvoiceIds.contains(invoice.id);
          
          return InvoiceCard(
            invoice: invoice,
            isSelected: isSelected,
            isSelectionMode: _isSelectionMode,
            onTap: () => _handleInvoiceTap(invoice),
            onLongPress: () => _handleInvoiceLongPress(invoice),
            onSelectionChanged: (selected) => _handleInvoiceSelection(invoice.id, selected ?? false),
          );
        },
      ),
    );
  }

  Widget _buildSelectionBottomBar() {
    return BottomAppBar(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            TextButton.icon(
              onPressed: _selectedInvoiceIds.isEmpty ? null : _markSelectedAsSent,
              icon: const Icon(Icons.send),
              label: const Text('Mark Sent'),
            ),
            TextButton.icon(
              onPressed: _selectedInvoiceIds.isEmpty ? null : _markSelectedAsPaid,
              icon: const Icon(Icons.payment),
              label: const Text('Mark Paid'),
            ),
            TextButton.icon(
              onPressed: _selectedInvoiceIds.isEmpty ? null : _deleteSelectedInvoices,
              icon: const Icon(Icons.delete),
              label: const Text('Delete'),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
            ),
          ],
        ),
      ),
    );
  }

  // =================== EVENT HANDLERS ===================

  void _handleMenuAction(String action) {
    switch (action) {
      case 'statistics':
        _showStatistics();
        break;
      case 'overdue':
        _showOverdueInvoices();
        break;
      case 'export':
        _exportData();
        break;
    }
  }

  void _handleInvoiceTap(Invoice invoice) {
    if (_isSelectionMode) {
      _handleInvoiceSelection(invoice.id, !_selectedInvoiceIds.contains(invoice.id));
    } else {
      _navigateToInvoiceDetail(invoice);
    }
  }

  void _handleInvoiceLongPress(Invoice invoice) {
    if (!_isSelectionMode) {
      setState(() {
        _isSelectionMode = true;
        _selectedInvoiceIds = [invoice.id];
      });
    }
  }

  void _handleInvoiceSelection(String invoiceId, bool selected) {
    setState(() {
      if (selected) {
        _selectedInvoiceIds.add(invoiceId);
      } else {
        _selectedInvoiceIds.remove(invoiceId);
      }

      if (_selectedInvoiceIds.isEmpty) {
        _isSelectionMode = false;
      }
    });
  }

  // =================== NAVIGATION ===================

  void _navigateToCreateInvoice() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CreateInvoiceScreen(),
      ),
    );
  }

  void _navigateToInvoiceDetail(Invoice invoice) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => InvoiceDetailScreen(invoice: invoice),
      ),
    );
  }

  // =================== SELECTION ACTIONS ===================

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedInvoiceIds.clear();
    });
  }

  void _selectAllInvoices() {
    final state = context.read<InvoiceBloc>().state;
    if (state is InvoicesLoaded) {
      setState(() {
        _selectedInvoiceIds = state.filteredInvoices.map((invoice) => invoice.id).toList();
      });
    }
  }

  void _markSelectedAsSent() {
    if (_selectedInvoiceIds.isNotEmpty) {
      context.read<InvoiceBloc>().add(
        BulkUpdateInvoiceStatus(
          invoiceIds: _selectedInvoiceIds,
          status: InvoiceStatus.sent,
        ),
      );
    }
  }

  void _markSelectedAsPaid() {
    if (_selectedInvoiceIds.isNotEmpty) {
      // For bulk operations, we'll mark as paid without payment records
      for (final invoiceId in _selectedInvoiceIds) {
        context.read<InvoiceBloc>().add(MarkInvoiceAsPaid(invoiceId: invoiceId));
      }
      _exitSelectionMode();
    }
  }

  void _deleteSelectedInvoices() {
    if (_selectedInvoiceIds.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Invoices'),
        content: Text(
          'Are you sure you want to delete ${_selectedInvoiceIds.length} invoice(s)? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<InvoiceBloc>().add(
                BulkDeleteInvoices(_selectedInvoiceIds),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  // =================== MENU ACTIONS ===================

  void _showStatistics() {
    context.read<InvoiceBloc>().add(const LoadInvoiceStatistics());

    showDialog(
      context: context,
      builder: (context) => BlocBuilder<InvoiceBloc, InvoiceState>(
        builder: (context, state) {
          if (state is InvoiceStatisticsLoaded) {
            return _buildStatisticsDialog(state.statistics);
          } else if (state is InvoiceLoading) {
            return const AlertDialog(
              content: SizedBox(
                height: 100,
                child: Center(child: CircularProgressIndicator()),
              ),
            );
          } else {
            return const AlertDialog(
              title: Text('Error'),
              content: Text('Failed to load statistics'),
            );
          }
        },
      ),
    );
  }

  Widget _buildStatisticsDialog(Map<String, dynamic> statistics) {
    final currencyFormat = NumberFormat.currency(symbol: '₹');

    return AlertDialog(
      title: const Text('Invoice Statistics'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatRow('Total Invoices', statistics['totalInvoices'].toString()),
            _buildStatRow('Paid Invoices', statistics['paidInvoices'].toString()),
            _buildStatRow('Overdue Invoices', statistics['overdueInvoices'].toString()),
            _buildStatRow('Draft Invoices', statistics['draftInvoices'].toString()),
            const Divider(),
            _buildStatRow('Total Amount', currencyFormat.format(statistics['totalAmount'])),
            _buildStatRow('Paid Amount', currencyFormat.format(statistics['paidAmount'])),
            _buildStatRow('Pending Amount', currencyFormat.format(statistics['pendingAmount'])),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  void _showOverdueInvoices() {
    context.read<InvoiceBloc>().add(const LoadOverdueInvoices());

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(title: const Text('Overdue Invoices')),
          body: BlocBuilder<InvoiceBloc, InvoiceState>(
            builder: (context, state) {
              if (state is OverdueInvoicesLoaded) {
                if (state.overdueInvoices.isEmpty) {
                  return const EmptyStateWidget(
                    icon: Icons.check_circle,
                    title: 'No Overdue Invoices',
                    subtitle: 'All invoices are up to date!',
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: state.overdueInvoices.length,
                  itemBuilder: (context, index) {
                    final invoice = state.overdueInvoices[index];
                    return InvoiceCard(
                      invoice: invoice,
                      onTap: () => _navigateToInvoiceDetail(invoice),
                    );
                  },
                );
              } else if (state is InvoiceLoading) {
                return const LoadingWidget();
              } else {
                return const Center(
                  child: Text('Failed to load overdue invoices'),
                );
              }
            },
          ),
        ),
      ),
    );
  }

  void _exportData() {
    // TODO: Implement data export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Export functionality coming soon!'),
      ),
    );
  }
}
